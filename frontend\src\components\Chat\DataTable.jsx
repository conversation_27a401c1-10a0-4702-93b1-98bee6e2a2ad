
import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';


const DataTable = ({ data, maxHeight = "400px", isIntermediate = false }) => {
  const { t } = useTranslation();

  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className="text-center py-4 text-sm text-gray-500 dark:text-gray-400">
        {t('datatable.noData', { defaultValue: 'No data to display' })}
      </div>
    );
  }

  const headers = Object.keys(data[0]);

  return (
    <div className="rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex flex-col">
      {/* Table container with both horizontal and vertical scrolling */}
      <div
        className="overflow-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800 flex-1"
        style={{ maxHeight: maxHeight }}
      >
        <table className="min-w-full">
          {/* Sticky header */}
          <thead className="bg-gray-50 dark:bg-gray-900 sticky top-0 z-10">
            <tr>
              {headers.map((header, index) => (
                <th
                  key={index}
                  className="px-4 py-3 text-left text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className={`
                  ${rowIndex % 2 === 0
                    ? 'bg-white dark:bg-gray-800'
                    : 'bg-gray-50 dark:bg-gray-900'
                  }
                  hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors
                `}
              >
                {headers.map((header, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100 whitespace-nowrap"
                  >
                    {row[header] !== null && row[header] !== undefined
                      ? String(row[header])
                      : t('datatable.emptyCell', { defaultValue: '-' })
                    }
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Row count indicator - fixed at bottom, doesn't scroll */}
      <div className="px-4 py-2 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
        <div className="flex justify-between items-center">
          <span>
            {t('datatable.showingRows', {
              count: data.length,
              defaultValue: 'Showing {{count}} row',
              defaultValue_other: 'Showing {{count}} rows'
            })}
          </span>
          {data.length >= 100 && (
            <span className="text-blue-600 dark:text-blue-400">
              {t('datatable.scrollHint', { defaultValue: '💡 Scroll to view more data' })}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};


DataTable.propTypes = {
  data: PropTypes.array.isRequired,
  maxHeight: PropTypes.string,
  isIntermediate: PropTypes.bool
};

export default DataTable;
