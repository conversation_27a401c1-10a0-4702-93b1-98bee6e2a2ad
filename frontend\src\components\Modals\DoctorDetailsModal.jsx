import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import authFetch from '../../utils/authFetch';
import { useTranslation } from 'react-i18next';
import LoadingSpinner from '../UI/LoadingSpinner';

const DoctorDetailsModal = ({ isOpen, onClose, doctorId, onPatientClick, onConsultationClick }) => {
  const { t } = useTranslation();
  const [doctorData, setDoctorData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [isAnimating, setIsAnimating] = useState(false);
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    if (isOpen && doctorId) {
      setIsAnimating(true);
      setShowContent(false);
      fetchDoctorDetails();
    } else if (!isOpen) {
      setIsAnimating(false);
      setShowContent(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, doctorId]);

  const fetchDoctorDetails = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await authFetch(`/api/v1/doctors/${doctorId}/details`);
      const data = await response.json();
      
      if (response.ok) {
        setDoctorData(data.data);
        // Smooth transition to show content
        setTimeout(() => setShowContent(true), 100);
      } else {
        setError(data.error || 'Failed to fetch doctor details');
      }
    } catch (error) {
      console.error('Error fetching doctor details:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return 'N/A';
    try {
      return new Date(dateTimeString).toLocaleString();
    } catch {
      return dateTimeString;
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black transition-opacity duration-300 flex items-center justify-center z-50 p-4 ${
      isOpen ? 'bg-opacity-50' : 'bg-opacity-0'
    }`}>
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full h-[75vh] flex flex-col overflow-hidden transform transition-all duration-300 ${
        isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            {t('modals.doctorDetails.title', { defaultValue: 'Doctor Details' })}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            aria-label={t('buttons.close', { defaultValue: 'Close' })}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 flex min-h-0">
          {loading ? (
            <div className="flex items-center justify-center flex-1">
              <LoadingSpinner />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center flex-1 p-4">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 max-w-md">
                <p className="text-red-700 dark:text-red-400">{error}</p>
                <button
                  onClick={fetchDoctorDetails}
                  className="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 underline"
                >
                  {t('errors.tryAgain', { defaultValue: 'Try again' })}
                </button>
              </div>
            </div>
          ) : doctorData ? (
            <div className="flex flex-1 min-h-0">
              {/* Sidebar Navigation */}
              <div className="w-48 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-3 flex-shrink-0">
                <nav className="space-y-2">
                  {[
                    { id: 'overview', label: t('modals.doctorDetails.tabs.overview', { defaultValue: 'Overview' }), icon: 'fas fa-user-md' },
                    { id: 'consultations', label: t('modals.doctorDetails.tabs.consultations', { defaultValue: 'Consultations' }), icon: 'fas fa-stethoscope' },
                    { id: 'patients', label: t('modals.doctorDetails.tabs.patients', { defaultValue: 'Patients' }), icon: 'fas fa-users' },
                    { id: 'statistics', label: t('modals.doctorDetails.tabs.statistics', { defaultValue: 'Statistics' }), icon: 'fas fa-chart-bar' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full text-left px-3 py-2.5 rounded text-sm font-medium transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 shadow-sm'
                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200'
                      }`}
                    >
                      <i className={`${tab.icon} mr-3 text-base`}></i>
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Main Content */}
              <div className={`flex-1 overflow-y-auto p-4 min-h-0 transition-opacity duration-300 ${
                showContent && !loading ? 'opacity-100' : 'opacity-0'
              }`}>
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                        {t('modals.doctorDetails.sections.doctorInfo', { defaultValue: 'Doctor Information' })}
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">{t('modals.doctorDetails.fields.fullName', { defaultValue: 'Full Name' })}</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{doctorData.doctor_info.full_name || t('common.na', { defaultValue: 'N/A' })}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">{t('modals.doctorDetails.fields.doctorId', { defaultValue: 'Doctor ID' })}</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{doctorData.doctor_info.doctor_id || t('common.na', { defaultValue: 'N/A' })}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">{t('modals.doctorDetails.fields.specialty', { defaultValue: 'Specialty' })}</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{doctorData.doctor_info.specialty || t('common.na', { defaultValue: 'N/A' })}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">{t('modals.doctorDetails.fields.email', { defaultValue: 'Email' })}</label>
                          <p className="text-base text-gray-900 dark:text-gray-100">{doctorData.doctor_info.email || t('common.na', { defaultValue: 'N/A' })}</p>
                        </div>
                      </div>
                    </div>

                    {/* Quick Statistics */}
                    {doctorData.statistics && (
                      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                          {t('modals.doctorDetails.sections.practiceOverview', { defaultValue: 'Practice Overview' })}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                              {doctorData.statistics.total_consultations || 0}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">{t('modals.doctorDetails.stats.totalConsultations', { defaultValue: 'Total Consultations' })}</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                              {doctorData.statistics.total_patients || 0}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">{t('modals.doctorDetails.stats.patientsTreated', { defaultValue: 'Patients Treated' })}</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                              {doctorData.statistics.active_days || 0}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">{t('modals.doctorDetails.stats.activeDays', { defaultValue: 'Active Days' })}</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'consultations' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{t('modals.doctorDetails.sections.recentConsultations', { defaultValue: 'Recent Consultations' })}</h3>
                    {doctorData.consultations.length > 0 ? (
                      <div className="space-y-4">
                        {doctorData.consultations.map((consultation, index) => (
                          <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <button
                                onClick={() => onConsultationClick && onConsultationClick(consultation.consultation_id)}
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline font-medium"
                                aria-label={t('modals.doctorDetails.buttons.viewConsultation', { defaultValue: 'View Consultation' })}
                              >
                                {t('modals.doctorDetails.consultationId', { consultationId: consultation.consultation_id, defaultValue: 'Consultation #{{consultationId}}' })}
                              </button>
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {formatDateTime(consultation.date)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                              {t('modals.doctorDetails.labels.patient', { defaultValue: 'Patient:' })} 
                              <button
                                onClick={() => onPatientClick && onPatientClick(consultation.patient_id)}
                                className="ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                                aria-label={t('modals.doctorDetails.buttons.viewPatient', { defaultValue: 'View Patient' })}
                              >
                                {consultation.patient_name}
                              </button>
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {t('modals.doctorDetails.labels.diagnosis', { defaultValue: 'Diagnosis:' })} {consultation.diagnosis || t('common.na', { defaultValue: 'N/A' })}
                            </p>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">{t('modals.doctorDetails.noConsultations', { defaultValue: 'No consultations found.' })}</p>
                    )}
                  </div>
                )}

                {activeTab === 'patients' && (
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{t('modals.doctorDetails.sections.patientsTreated', { defaultValue: 'Patients Treated' })}</h3>
                    {doctorData.patients.length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                          <thead className="bg-gray-50 dark:bg-gray-900">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">{t('modals.doctorDetails.tableHeaders.patientName', { defaultValue: 'Patient Name' })}</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">{t('modals.doctorDetails.tableHeaders.consultations', { defaultValue: 'Consultations' })}</th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">{t('modals.doctorDetails.tableHeaders.lastVisit', { defaultValue: 'Last Visit' })}</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {doctorData.patients.map((patient, index) => (
                              <tr key={index}>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  <button
                                    onClick={() => onPatientClick && onPatientClick(patient.patient_id)}
                                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                                    aria-label={t('modals.doctorDetails.buttons.viewPatient', { defaultValue: 'View Patient' })}
                                  >
                                    {patient.patient_name}
                                  </button>
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {patient.consultation_count || 0}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {formatDateTime(patient.last_consultation)}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">{t('modals.doctorDetails.noPatients', { defaultValue: 'No patients found.' })}</p>
                    )}
                  </div>
                )}

                {activeTab === 'statistics' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{t('modals.doctorDetails.sections.practiceStatistics', { defaultValue: 'Practice Statistics' })}</h3>
                    {doctorData.statistics ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                          <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{t('modals.doctorDetails.sections.activitySummary', { defaultValue: 'Activity Summary' })}</h4>
                          <div className="space-y-3">
                            <div className="flex justify-between">
                              <span className="text-gray-600 dark:text-gray-400">{t('modals.doctorDetails.stats.totalConsultationsColon', { defaultValue: 'Total Consultations:' })}</span>
                              <span className="font-medium text-gray-900 dark:text-gray-100">{doctorData.statistics.total_consultations || 0}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600 dark:text-gray-400">{t('modals.doctorDetails.stats.uniquePatients', { defaultValue: 'Unique Patients:' })}</span>
                              <span className="font-medium text-gray-900 dark:text-gray-100">{doctorData.statistics.total_patients || 0}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600 dark:text-gray-400">{t('modals.doctorDetails.stats.activeDaysColon', { defaultValue: 'Active Days:' })}</span>
                              <span className="font-medium text-gray-900 dark:text-gray-100">{doctorData.statistics.active_days || 0}</span>
                            </div>
                          </div>
                        </div>
                        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                          <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{t('modals.doctorDetails.sections.practicePeriod', { defaultValue: 'Practice Period' })}</h4>
                          <div className="space-y-3">
                            <div>
                              <span className="text-gray-600 dark:text-gray-400">{t('modals.doctorDetails.stats.firstConsultation', { defaultValue: 'First Consultation:' })}</span>
                              <p className="font-medium text-gray-900 dark:text-gray-100">{formatDateTime(doctorData.statistics.first_consultation)}</p>
                            </div>
                            <div>
                              <span className="text-gray-600 dark:text-gray-400">{t('modals.doctorDetails.stats.lastConsultation', { defaultValue: 'Last Consultation:' })}</span>
                              <p className="font-medium text-gray-900 dark:text-gray-100">{formatDateTime(doctorData.statistics.last_consultation)}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400">{t('modals.doctorDetails.noStatistics', { defaultValue: 'No statistics available.' })}</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};


DoctorDetailsModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  doctorId: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number
  ]),
  onPatientClick: PropTypes.func,
  onConsultationClick: PropTypes.func
};

export default DoctorDetailsModal;
