import argparse
from utils.database_manager import DatabaseManager

def main():
    parser = argparse.ArgumentParser(description="Database Management CLI")
    parser.add_argument('--purge', action='store_true', help='Completely purge all tables and data')
    parser.add_argument('--recreate', action='store_true', help='Recreate database schema')
    parser.add_argument('--sync-users', action='store_true', help='Sync user data from hospital database')
    parser.add_argument('--all', action='store_true', help='Run purge, recreate, and sync-users in sequence')
    args = parser.parse_args()

    manager = DatabaseManager()

    if args.all:
        print("[WARNING] This will COMPLETELY PURGE the app database, RECREATE schema, and SYNC users. ALL DATA WILL BE LOST!")
        confirmation = input("Type 'ALL_DATABASE_RESET' to confirm: ")
        if confirmation != 'ALL_DATABASE_RESET':
            print("[ERROR] Operation cancelled.")
            return
        success = manager.purge_all_database()
        if not success:
            print("[ERROR] Purge failed! Aborting sequence.")
            return
        print("[SUCCESS] Purge completed. Proceeding to recreate schema...")
        success = manager.recreate_database()
        if not success:
            print("[ERROR] Recreate schema failed! Aborting sequence.")
            return
        print("[SUCCESS] Schema recreated. Proceeding to sync users...")
        success = manager.sync_users()
        if success:
            print("[COMPLETE] All operations completed successfully!")
        else:
            print("[ERROR] User sync failed! Check logs for details.")
        return

    if args.purge:
        print("[WARNING] This will COMPLETELY PURGE the app database. ALL DATA WILL BE LOST!")
        confirmation = input("Type 'PURGE_DATABASE' to confirm: ")
        if confirmation != 'PURGE_DATABASE':
            print("[ERROR] Operation cancelled.")
            return
        success = manager.purge_all_database()
    elif args.recreate:
        print("[WARNING] This will RECREATE the app database schema.")
        confirmation = input("Type 'RECREATE_DATABASE' to confirm: ")
        if confirmation != 'RECREATE_DATABASE':
            print("[ERROR] Operation cancelled.")
            return
        success = manager.recreate_database()
    elif args.sync_users:
        print("[WARNING] This will SYNC users from the hospital database.")
        success = manager.sync_users()
    else:
        parser.print_help()
        return

    if success:
        print("[SUCCESS] Operation completed successfully!")
    else:
        print("[ERROR] Operation failed! Check logs for details.")

if __name__ == "__main__":
    main()