import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import authFetch from '../utils/authFetch';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';

const Settings = () => {
  const { t } = useTranslation();
  const { theme, toggleTheme } = useTheme();
  const { user } = useAuth();
  
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: false,
      sms: false
    },
    display: {
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/DD/YYYY'
    },
    privacy: {
      shareData: false,
      analytics: true
    },
    system: {
      autoSave: true,
      sessionTimeout: 30
    }
  });
  
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/v0/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(prev => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error(t('errors.loadSettings', { defaultValue: 'Error loading settings:' }), error);
    }
  };

  const saveSettings = async () => {
    setIsSaving(true);
    setSaveMessage('');

    try {
      const response = await authFetch('/api/v0/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      if (response.ok) {
        setSaveMessage(t('settings.saveSuccess', { defaultValue: 'Settings saved successfully!' }));
        setTimeout(() => setSaveMessage(''), 3000);
      } else {
        setSaveMessage(t('settings.saveFailed', { defaultValue: 'Failed to save settings. Please try again.' }));
      }
    } catch (error) {
      console.error(t('errors.saveSettings', { defaultValue: 'Error saving settings:' }), error);
      setSaveMessage(t('errors.network', { defaultValue: 'Network error. Please check your connection and try again.' }));
    } finally {
      setIsSaving(false);
    }
  };

  // Only update the changed field to avoid replacing the whole object
  const updateSetting = (category, key, value) => {
    setSettings(prev => {
      if (prev[category][key] === value) return prev;
      return {
        ...prev,
        [category]: {
          ...prev[category],
          [key]: value
        }
      };
    });
  };

  // Memoize SettingSection to prevent unnecessary remounts
  const SettingSection = React.memo(function SettingSection({ title, icon, children }) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-4">
          <i className={`${icon} mr-3 text-blue-600 dark:text-blue-400`}></i>
          {title}
        </h3>
        {children}
      </div>
    );
  });

  SettingSection.propTypes = {
    title: PropTypes.node.isRequired,
    icon: PropTypes.string.isRequired,
    children: PropTypes.node
  };

  const ToggleSwitch = ({ enabled, onChange, label, description }) => (
    <div className="flex items-center justify-between py-3">
      <div>
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{label}</div>
        {description && (
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">{description}</div>
        )}
      </div>
      <button
        onClick={() => onChange(!enabled)}
        className={`
          relative inline-flex h-6 w-11 items-center rounded-full transition-colors
          ${enabled ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'}
        `}
        aria-label={enabled
          ? t('settings.toggleOn', { defaultValue: 'Turn off' })
          : t('settings.toggleOff', { defaultValue: 'Turn on' })}
      >
        <span
          className={`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform
            ${enabled ? 'translate-x-6' : 'translate-x-1'}
          `}
        />
      </button>
    </div>
  );

  ToggleSwitch.propTypes = {
    enabled: PropTypes.bool.isRequired,
    onChange: PropTypes.func.isRequired,
    label: PropTypes.node.isRequired,
    description: PropTypes.node
  };


  // Change Password Form as a memoized component to isolate state
  const ChangePasswordForm = React.memo(function ChangePasswordForm() {
    const [pwForm, setPwForm] = useState({ current: '', new: '', confirm: '' });
    const [pwLoading, setPwLoading] = useState(false);
    const [pwMessage, setPwMessage] = useState('');
    const { t } = useTranslation();
    // Debug: log on every render
    console.log('ChangePasswordForm rendered');

    const handlePwChange = (e) => {
      const { name, value } = e.target;
      setPwForm((prev) => ({ ...prev, [name]: value }));
    };

    const handleChangePassword = async (e) => {
      e.preventDefault();
      setPwMessage('');
      if (!pwForm.current || !pwForm.new || !pwForm.confirm) {
        setPwMessage(t('settings.pwRequired', { defaultValue: 'All fields are required.' }));
        return;
      }
      if (pwForm.new !== pwForm.confirm) {
        setPwMessage(t('settings.pwNoMatch', { defaultValue: 'New passwords do not match.' }));
        return;
      }
      setPwLoading(true);
      try {
        // Placeholder: update endpoint when backend is ready
        const response = await authFetch('/api/auth/change_password', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            current_password: pwForm.current,
            new_password: pwForm.new,
            confirm_new_password: pwForm.confirm
          })
        });
        if (response.ok) {
          setPwMessage(t('settings.pwSuccess', { defaultValue: 'Password changed successfully.' }));
          setPwForm({ current: '', new: '', confirm: '' });
        } else {
          const data = await response.json().catch(() => ({}));
          setPwMessage(data?.message || t('settings.pwFailed', { defaultValue: 'Failed to change password.' }));
        }
      } catch (err) {
        setPwMessage(t('settings.pwNetwork', { defaultValue: 'Network error. Please try again.' }));
      } finally {
        setPwLoading(false);
      }
    };

    return (
      <form onSubmit={handleChangePassword} className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t('settings.currentPassword', { defaultValue: 'Current Password' })}
          </label>
          <input
            type="password"
            name="current"
            value={pwForm.current}
            onChange={handlePwChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            autoComplete="current-password"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t('settings.newPassword', { defaultValue: 'New Password' })}
          </label>
          <input
            type="password"
            name="new"
            value={pwForm.new}
            onChange={handlePwChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            autoComplete="new-password"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t('settings.confirmNewPassword', { defaultValue: 'Confirm New Password' })}
          </label>
          <input
            type="password"
            name="confirm"
            value={pwForm.confirm}
            onChange={handlePwChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            autoComplete="new-password"
            required
          />
        </div>
        {pwMessage && (
          <div className={`text-sm rounded px-2 py-1 ${pwMessage.includes(t('settings.pwSuccess', { defaultValue: 'Password changed successfully.' })) ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300' : 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300'}`}>
            {pwMessage}
          </div>
        )}
        <button
          type="submit"
          className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md transition-colors"
          disabled={pwLoading}
        >
          {pwLoading ? (
            <i className="fas fa-spinner animate-spin mr-2"></i>
          ) : (
            <i className="fas fa-key mr-2"></i>
          )}
          {pwLoading
            ? t('settings.pwChanging', { defaultValue: 'Changing...' })
            : t('settings.pwChangeButton', { defaultValue: 'Change Password' })}
        </button>
      </form>
    );
  });

  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
          <i className="fas fa-cog mr-3"></i>
          {t('settings.title', { defaultValue: 'Settings' })}
        </h2>
        <button
          onClick={saveSettings}
          disabled={isSaving}
          className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors flex items-center"
        >
          {isSaving ? (
            <i className="fas fa-spinner animate-spin mr-2"></i>
          ) : (
            <i className="fas fa-save mr-2"></i>
          )}
          {isSaving
            ? t('settings.saving', { defaultValue: 'Saving...' })
            : t('settings.saveButton', { defaultValue: 'Save Settings' })}
        </button>
      </div>

      {/* Save Message */}
      {saveMessage && (
        <div className={`mb-6 p-4 rounded-lg ${
          saveMessage.includes(t('settings.saveSuccess', { defaultValue: 'Settings saved successfully!' }))
            ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border border-green-300 dark:border-green-700'
            : 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 border border-red-300 dark:border-red-700'
        }`}>
          <i className={`fas ${saveMessage.includes(t('settings.saveSuccess', { defaultValue: 'Settings saved successfully!' })) ? 'fa-check-circle' : 'fa-exclamation-triangle'} mr-2`}></i>
          {saveMessage}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column */}
        <div>
          {/* User Profile */}
          <SettingSection title={t('settings.userProfile', { defaultValue: 'User Profile' })} icon="fas fa-user">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('settings.displayName', { defaultValue: 'Display Name' })}
                </label>
                <input
                  type="text"
                  value={user?.name || ''}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  aria-label={t('settings.displayName', { defaultValue: 'Display Name' })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('settings.username', { defaultValue: 'Username' })}
                </label>
                <input
                  type="text"
                  value={user?.username || ''}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  aria-label={t('settings.username', { defaultValue: 'Username' })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('settings.role', { defaultValue: 'Role' })}
                </label>
                <input
                  type="text"
                  value={user?.role || ''}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  aria-label={t('settings.role', { defaultValue: 'Role' })}
                />
              </div>
              {/* Change Password Section */}
              <div className="pt-6 border-t border-gray-200 dark:border-gray-700 mt-6">
                <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                  <i className="fas fa-key mr-2 text-blue-600 dark:text-blue-400"></i>
                  {t('settings.changePassword', { defaultValue: 'Change Password' })}
                </h4>
                <ChangePasswordForm />
              </div>
            </div>
          </SettingSection>

          {/* Notifications */}
          <SettingSection title={t('settings.notifications', { defaultValue: 'Notifications' })} icon="fas fa-bell">
            <div className="space-y-2">
              <ToggleSwitch
                enabled={settings.notifications.email}
                onChange={(value) => updateSetting('notifications', 'email', value)}
                label={t('settings.emailNotifications', { defaultValue: 'Email Notifications' })}
                description={t('settings.emailNotificationsDesc', { defaultValue: 'Receive notifications via email' })}
              />
              <ToggleSwitch
                enabled={settings.notifications.push}
                onChange={(value) => updateSetting('notifications', 'push', value)}
                label={t('settings.pushNotifications', { defaultValue: 'Push Notifications' })}
                description={t('settings.pushNotificationsDesc', { defaultValue: 'Receive browser push notifications' })}
              />
            </div>
          </SettingSection>
        </div>

        {/* Right Column */}
        <div>
          {/* Appearance */}
          <SettingSection title={t('settings.appearance', { defaultValue: 'Appearance' })} icon="fas fa-palette">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{t('settings.theme', { defaultValue: 'Theme' })}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {t('settings.currentTheme', { defaultValue: 'Current: {{mode}} mode', mode: theme === 'dark' ? t('settings.dark', { defaultValue: 'Dark' }) : t('settings.light', { defaultValue: 'Light' }) })}
                  </div>
                </div>
                <button
                  onClick={toggleTheme}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 rounded-md transition-colors flex items-center"
                  aria-label={t('settings.switchTheme', { defaultValue: 'Switch theme' })}
                >
                  <i className={`fas ${theme === 'dark' ? 'fa-sun' : 'fa-moon'} mr-2`}></i>
                  {t('settings.switchTo', { defaultValue: 'Switch to {{mode}}', mode: theme === 'dark' ? t('settings.light', { defaultValue: 'Light' }) : t('settings.dark', { defaultValue: 'Dark' }) })}
                </button>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('settings.language', { defaultValue: 'Language' })}
                </label>
                <select
                  value={settings.display.language}
                  onChange={(e) => {
                    const lang = e.target.value;
                    updateSetting('display', 'language', lang);
                    import('../locales/i18n').then(({ default: i18n }) => {
                      i18n.changeLanguage(lang);
                    });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  aria-label={t('settings.language', { defaultValue: 'Language' })}
                >
                  <option value="en">{t('settings.english', { defaultValue: 'English' })}</option>
                  <option value="fr">{t('settings.french', { defaultValue: 'Français' })}</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('settings.dateFormat', { defaultValue: 'Date Format' })}
                </label>
                <select
                  value={settings.display.dateFormat}
                  onChange={(e) => updateSetting('display', 'dateFormat', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  aria-label={t('settings.dateFormat', { defaultValue: 'Date Format' })}
                >
                  <option value="MM/DD/YYYY">{t('settings.dateFormatUs', { defaultValue: 'MM/DD/YYYY' })}</option>
                  <option value="DD/MM/YYYY">{t('settings.dateFormatEu', { defaultValue: 'DD/MM/YYYY' })}</option>
                  <option value="YYYY-MM-DD">{t('settings.dateFormatIso', { defaultValue: 'YYYY-MM-DD' })}</option>
                </select>
              </div>
            </div>
          </SettingSection>

          {/* About */}
          <SettingSection title={t('settings.about', { defaultValue: 'About' })} icon="fas fa-info-circle">
            <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex justify-between">
                <span>{t('settings.version', { defaultValue: 'Version:' })}</span>
                <span className="font-mono">1.0.0</span>
              </div>
              <div className="flex justify-between">
                <span>{t('settings.build', { defaultValue: 'Build:' })}</span>
                <span className="font-mono">2077.01.01</span>
              </div>
              <div className="flex justify-between">
                <span>{t('settings.lastUpdated', { defaultValue: 'Last Updated:' })}</span>
                <span>{t('settings.lastUpdatedDate', { defaultValue: 'January 1, 2077' })}</span>
              </div>
              <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                <p className="text-xs">
                  {t('settings.aboutDesc', { defaultValue: 'Hospital Assistant System - AI-powered hospital management platform' })}
                </p>
              </div>
            </div>
          </SettingSection>
        </div>
      </div>
    </div>
  );
};

export default Settings;
