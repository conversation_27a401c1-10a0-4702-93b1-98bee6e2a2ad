
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { useChat } from '../../contexts/ChatContext';


const ChatHistorySidebar = ({ isOpen, onToggle }) => {
  const { t } = useTranslation();
  const {
    chatSessions,
    currentSessionId,
    isSwitchingChat,
    createNewSession,
    switchToSession,
    deleteSession,
    renameSession,
    duplicateSession,
    clearAllSessions
  } = useChat();

  const [editingSessionId, setEditingSessionId] = useState(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [switchingToSessionId, setSwitchingToSessionId] = useState(null);

  // Filter and sort sessions - ensure proper chronological ordering
  const filteredSessions = Object.values(chatSessions)
    .filter(session => {
      if (!searchQuery.trim()) return true;
      const query = searchQuery.toLowerCase();
      return (
        session.title.toLowerCase().includes(query) ||
        session.messages.some(msg =>
          msg.content.toLowerCase().includes(query)
        )
      );
    })
    .sort((a, b) => {
      // Ensure proper date comparison - most recent first
      const dateA = new Date(a.updatedAt);
      const dateB = new Date(b.updatedAt);
      return dateB.getTime() - dateA.getTime();
    });

  const handleStartEdit = (session) => {
    setEditingSessionId(session.id);
    setEditingTitle(session.title);
  };

  const handleSaveEdit = () => {
    if (editingTitle.trim()) {
      renameSession(editingSessionId, editingTitle.trim());
    }
    setEditingSessionId(null);
    setEditingTitle('');
  };

  const handleCancelEdit = () => {
    setEditingSessionId(null);
    setEditingTitle('');
  };

  const handleDeleteClick = (sessionId) => {
    setShowDeleteConfirm(sessionId);
  };

  const handleConfirmDelete = () => {
    deleteSession(showDeleteConfirm);
    setShowDeleteConfirm(null);
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(null);
  };

  const formatDate = (date) => {
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    if (diffInHours < 1) {
      return t('chatHistorySidebar.justNow', { defaultValue: 'Just now' });
    } else if (diffInHours < 24) {
      return t('chatHistorySidebar.hoursAgo', { count: Math.floor(diffInHours), defaultValue: '{{count}}h ago' });
    } else if (diffInHours < 48) {
      return t('chatHistorySidebar.yesterday', { defaultValue: 'Yesterday' });
    } else {
      return date.toLocaleDateString();
    }
  };

  const getSessionPreview = (session) => {
    const userMessages = session.messages.filter(msg => msg.type === 'user');
    if (userMessages.length > 0) {
      return userMessages[userMessages.length - 1].content.substring(0, 50) + '...';
    }
    return t('chatHistorySidebar.newConversation', { defaultValue: 'New conversation' });
  };

  return (
    <>
      <div className="w-full flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            <i className="fas fa-history mr-2"></i>
            {t('chatHistorySidebar.title', { defaultValue: 'Chat History' })}
          </h3>
        </div>

        {/* New Chat Button */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => createNewSession()}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 mb-3"
            aria-label={t('chatHistorySidebar.newChat', { defaultValue: 'New Chat' })}
          >
            <i className="fas fa-plus mr-2"></i>
            {t('chatHistorySidebar.newChat', { defaultValue: 'New Chat' })}
          </button>

          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder={t('chatHistorySidebar.searchPlaceholder', { defaultValue: 'Search conversations...' })}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 pl-9 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 session-search"
              aria-label={t('chatHistorySidebar.searchAria', { defaultValue: 'Search conversations' })}
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm"></i>
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 text-sm"
                aria-label={t('chatHistorySidebar.clearSearch', { defaultValue: 'Clear search' })}
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>
        </div>

        {/* Sessions List */}
        <div className="flex-1 overflow-y-auto chat-history-sidebar">
          {filteredSessions.length === 0 ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400 empty-state">
              {searchQuery ? (
                <>
                  <i className="fas fa-search empty-state-icon block"></i>
                  <p>{t('chatHistorySidebar.noConversationsFound', { defaultValue: 'No conversations found' })}</p>
                  <p className="text-xs mt-1">{t('chatHistorySidebar.tryDifferentSearch', { defaultValue: 'Try a different search term' })}</p>
                </>
              ) : (
                <>
                  <i className="fas fa-comments empty-state-icon block"></i>
                  <p>{t('chatHistorySidebar.noChatSessions', { defaultValue: 'No chat sessions yet' })}</p>
                  <p className="text-xs mt-1">{t('chatHistorySidebar.startNewConversation', { defaultValue: 'Start a new conversation' })}</p>
                </>
              )}
            </div>
          ) : (
            <div className="p-2">
              {filteredSessions.map((session) => (
                <div
                  key={session.id}
                  className={`
                    group relative mb-2 p-3 rounded-lg cursor-pointer session-item sidebar-transition
                    ${currentSessionId === session.id
                      ? 'bg-blue-100 dark:bg-blue-900 border border-blue-300 dark:border-blue-700 active'
                      : switchingToSessionId === session.id
                      ? 'bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 opacity-75'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-700 border border-transparent'
                    }
                    ${switchingToSessionId === session.id ? 'session-loading' : ''}
                  `}
                  onClick={() => switchToSession(session.id)}
                >
                  {editingSessionId === session.id ? (
                    <div className="flex items-center space-x-2" onClick={(e) => e.stopPropagation()}>
                      <input
                        type="text"
                        value={editingTitle}
                        onChange={(e) => setEditingTitle(e.target.value)}
                        className="flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') handleSaveEdit();
                          if (e.key === 'Escape') handleCancelEdit();
                        }}
                        autoFocus
                      />
                      <button
                        onClick={handleSaveEdit}
                        className="text-green-600 hover:text-green-700 text-xs"
                      >
                        <i className="fas fa-check"></i>
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        className="text-red-600 hover:text-red-700 text-xs"
                      >
                        <i className="fas fa-times"></i>
                      </button>
                    </div>
                  ) : (
                    <>
                      <div
                        className="flex items-start justify-between cursor-pointer"
                        onClick={async () => {
                          if (session.id !== currentSessionId && !isSwitchingChat) {
                            setSwitchingToSessionId(session.id);
                            await switchToSession(session.id);
                            setSwitchingToSessionId(null);
                          }
                        }}
                      >
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                              {session.title}
                            </h4>
                            {switchingToSessionId === session.id && (
                              <div className="animate-spin rounded-full h-3 w-3 border border-blue-500 border-t-transparent"></div>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                            {getSessionPreview(session)}
                          </p>
                          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                            {formatDate(session.updatedAt)}
                          </p>
                        </div>
                        
                        {/* Action Buttons */}
                        <div className="flex items-center space-x-1 session-actions opacity-0 group-hover:opacity-100 transition-opacity">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleStartEdit(session);
                            }}
                            className="text-gray-400 hover:text-blue-600 text-xs p-1 tooltip"
                            data-tooltip={t('chatHistorySidebar.rename', { defaultValue: 'Rename' })}
                            aria-label={t('chatHistorySidebar.rename', { defaultValue: 'Rename' })}
                          >
                            <i className="fas fa-edit"></i>
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              duplicateSession(session.id);
                            }}
                            className="text-gray-400 hover:text-green-600 text-xs p-1 tooltip"
                            data-tooltip={t('chatHistorySidebar.duplicate', { defaultValue: 'Duplicate' })}
                            aria-label={t('chatHistorySidebar.duplicate', { defaultValue: 'Duplicate' })}
                          >
                            <i className="fas fa-copy"></i>
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteClick(session.id);
                            }}
                            className="text-gray-400 hover:text-red-600 text-xs p-1 tooltip"
                            data-tooltip={t('chatHistorySidebar.delete', { defaultValue: 'Delete' })}
                            aria-label={t('chatHistorySidebar.delete', { defaultValue: 'Delete' })}
                          >
                            <i className="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer Actions */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={clearAllSessions}
            className="w-full flex items-center justify-center px-4 py-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 border border-red-300 dark:border-red-600 rounded-lg transition-colors duration-200"
            aria-label={t('chatHistorySidebar.clearAllHistory', { defaultValue: 'Clear All History' })}
          >
            <i className="fas fa-trash-alt mr-2"></i>
            {t('chatHistorySidebar.clearAllHistory', { defaultValue: 'Clear All History' })}
          </button>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              {t('modals.deleteChatSession.title', { defaultValue: 'Delete Chat Session?' })}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {t('modals.deleteChatSession.warning', { defaultValue: 'This action cannot be undone. The chat session and all its messages will be permanently deleted.' })}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={handleConfirmDelete}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                aria-label={t('modals.deleteChatSession.delete', { defaultValue: 'Delete' })}
              >
                {t('modals.deleteChatSession.delete', { defaultValue: 'Delete' })}
              </button>
              <button
                onClick={handleCancelDelete}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                aria-label={t('modals.deleteChatSession.cancel', { defaultValue: 'Cancel' })}
              >
                {t('modals.deleteChatSession.cancel', { defaultValue: 'Cancel' })}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};


ChatHistorySidebar.propTypes = {
  isOpen: PropTypes.bool,
  onToggle: PropTypes.func
};

export default ChatHistorySidebar;
