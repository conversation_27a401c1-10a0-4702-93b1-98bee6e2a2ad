$ignoredFolders = @("node_modules", ".venv", "__pycache__", "_pyc", ".git") # Add all folders you want to ignore
Get-ChildItem -Path . -Recurse | Where-Object {
    $path = $_.FullName
    $ignore = $false
    foreach ($folder in $ignoredFolders) {
        if ($path -like "*\$folder*" -or $path -like "*\$folder\*") { # Check for folder name in path
            $ignore = $true
            break
        }
    }
    -not $ignore
} | ForEach-Object {
    $level = ($_.FullName.Length - ($_.FullName -replace '[^\\]').Length) / 1
    $indent = "  " * $level
    if ($_.PSIsContainer) {
        Write-Host "$indent$($_.Name)\"
    } else {
        Write-Host "$indent$($_.Name)"
    }
}