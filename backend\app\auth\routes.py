from datetime import datetime, timed<PERSON>ta
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import (
    HTTPBearer,
    HTTPAuthorizationCredentials,
    OAuth2PasswordRequestForm,
)
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel

from app.models import get_user_by_username, User
from app.models.user import update_user_last_login
from app.auth.security import (
    verify_password,
    create_access_token,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    decode_token,
)

router = APIRouter()


security = HTTPBearer()


# Pydantic models
class LoginRequest(BaseModel):
    username: str
    password: str


class Token(BaseModel):
    access_token: str
    token_type: str


class UserResponse(BaseModel):
    username: str
    role: str
    full_name: str
    department_name: str


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> User:
    """Get the current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    payload = decode_token(credentials.credentials)
    if not payload:
        raise credentials_exception

    username: str = payload.get("sub")
    if username is None:
        raise credentials_exception

    user = get_user_by_username(username)
    if user is None:
        raise credentials_exception
    return user


@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """OAuth2 compatible token endpoint"""
    user = get_user_by_username(form_data.username)

    if not user or not verify_password(form_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Update last login time
    user.last_login = datetime.now()
    update_user_last_login(user.username, user.last_login)

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}


@router.post("/login", response_model=dict)
async def login(login_data: LoginRequest, response: Response):
    """Handle user login"""
    user = get_user_by_username(login_data.username)

    if not user or not verify_password(login_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password",
        )

    # Update last login time
    user.last_login = datetime.now()
    update_user_last_login(user.username, user.last_login)

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Create refresh token
    refresh_token_expires = timedelta(days=7)
    refresh_token = create_access_token(
        data={"sub": user.username, "refresh": True},
        expires_delta=refresh_token_expires,
    )

    # Set refresh token in HTTP-only cookie
    response.set_cookie(
        key="refresh_token",
        value=refresh_token,
        httponly=True,
        secure=True,  # Set to True in production
        samesite="strict",
        max_age=7 * 24 * 60 * 60,  # 7 days
    )

    return {
        "type": "success",
        "message": "Login successful",
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "username": user.username,
            "role": user.role,
            "name": user.full_name,
            "department": user.department_name,
        },
    }


@router.get("/login")
async def login_page():
    """Serve the login page"""
    return FileResponse("static/login.html")


@router.post("/logout")
async def logout(request: Request, response: Response):
    """Handle user logout and revoke refresh token"""
    refresh_token = request.cookies.get("refresh_token")
    if refresh_token:
        token_revocation_list.add(refresh_token)
    response.delete_cookie("refresh_token")
    return {"type": "success", "message": "Logout successful"}


@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return UserResponse(
        username=current_user.username,
        role=current_user.role,
        full_name=current_user.full_name,
        department_name=current_user.department_name,
    )


# Token revocation list (in-memory for now, consider persistent storage for production)
token_revocation_list = set()


@router.post("/refresh")
async def refresh_token(request: Request):
    """Refresh access token using refresh token"""
    # Get refresh token from HTTP-only cookie
    refresh_token = request.cookies.get("refresh_token")
    if not refresh_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Refresh token missing"
        )

    # Check if token is revoked
    if refresh_token in token_revocation_list:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Refresh token revoked"
        )

    # Verify refresh token
    payload = decode_token(refresh_token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token"
        )

    # Get user from token
    username: str = payload.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token payload"
        )

    user = get_user_by_username(username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found"
        )

    # Create new access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Create new refresh token (rotate tokens)
    refresh_token_expires = timedelta(days=7)
    new_refresh_token = create_access_token(
        data={"sub": user.username, "refresh": True},
        expires_delta=refresh_token_expires,
    )

    # Revoke old refresh token
    token_revocation_list.add(refresh_token)

    # Set HTTP-only cookies
    response = JSONResponse(
        content={"access_token": access_token, "token_type": "bearer"}
    )
    response.set_cookie(
        key="refresh_token",
        value=new_refresh_token,
        httponly=True,
        secure=True,  # Set to True in production
        samesite="strict",
        max_age=7 * 24 * 60 * 60,  # 7 days
    )

    return response
