"""
Chat Session Service

This module provides business logic for chat session management,
including database operations for conversations and messages.
"""

import logging
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
from app.core.app_database import get_app_database_service
from app.models.chat import (
    CreateChatSessionRequest, CreateMessageRequest, UpdateChatSessionRequest,
    ChatSessionResponse, ChatMessageResponse, ChatSessionWithMessagesResponse,
    ChatSessionListResponse, ChatSessionListParams
)

logger = logging.getLogger(__name__)


class ChatSessionService:
    """Service for managing chat sessions and messages"""
    
    def __init__(self):
        self.db_service = get_app_database_service()
    
    def create_session(self, user_id: int, request: CreateChatSessionRequest) -> ChatSessionResponse:
        """Create a new chat session for a user"""
        try:
            # Generate default title if not provided
            title = request.title
            if not title:
                # Get session count for user to generate default title
                session_count = self._get_user_session_count(user_id)
                title = f"Chat {session_count + 1}"
            
            # Create conversation record
            query = """
                INSERT INTO conversation (user_id, started_at, last_activity)
                VALUES (%s, NOW(), NOW())
                RETURNING id, started_at, last_activity
            """
            
            with self.db_service.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, (user_id,))
                    result = cursor.fetchone()
                    conversation_id = result['id']
                    started_at = result['started_at']
                    last_activity = result['last_activity']
                    
                    # Create welcome message
                    welcome_content = "Welcome to the Hospital Data Assistant! Ask me questions about patient data, hospital data, or any other database information."
                    welcome_query = """
                        INSERT INTO chat_history (user_id, conversation_id, message_type, content_type, 
                                                content, is_hidden, created_at, topic_id)
                        VALUES (%s, %s, 'assistant', 'text', %s, false, NOW(), %s)
                    """
                    cursor.execute(welcome_query, (user_id, conversation_id, welcome_content, request.topic_id))
                    
                    conn.commit()
            
            return ChatSessionResponse(
                id=conversation_id,
                user_id=user_id,
                title=title,
                started_at=started_at,
                last_activity=last_activity,
                message_count=1
            )
            
        except Exception as e:
            logger.error(f"Error creating chat session for user {user_id}: {e}")
            raise
    
    def get_user_sessions(self, user_id: int, params: ChatSessionListParams) -> ChatSessionListResponse:
        """Get all chat sessions for a user with pagination and filtering"""
        try:
            # Build WHERE clause for search
            where_conditions = ["c.user_id = %s"]
            query_params = [user_id]
            
            if params.search:
                # For now, we'll search in the first message content since we don't have session titles stored
                where_conditions.append("EXISTS (SELECT 1 FROM chat_history ch WHERE ch.conversation_id = c.id AND ch.content ILIKE %s)")
                query_params.append(f"%{params.search}%")
            
            where_clause = " AND ".join(where_conditions)
            
            # Build ORDER BY clause
            order_mapping = {
                'last_activity': 'c.last_activity',
                'started_at': 'c.started_at',
                'title': 'c.started_at',  # Fallback to started_at for title sorting
                'id': 'c.id'
            }
            order_field = order_mapping.get(params.sort_by, 'c.last_activity')
            order_clause = f"ORDER BY {order_field} {params.sort_order.upper()}"
            
            # Count total sessions
            count_query = f"""
                SELECT COUNT(DISTINCT c.id)
                FROM conversation c
                WHERE {where_clause}
            """
            total_count = self.db_service.execute_single_query(count_query, tuple(query_params))['count']
            
            # Calculate pagination
            offset = (params.page - 1) * params.page_size
            total_pages = (total_count + params.page_size - 1) // params.page_size
            
            # Get sessions with message counts
            sessions_query = f"""
                SELECT c.id, c.user_id, c.started_at, c.last_activity,
                       COUNT(ch.id) as message_count,
                       COALESCE(
                           (SELECT ch2.content 
                            FROM chat_history ch2 
                            WHERE ch2.conversation_id = c.id 
                              AND ch2.message_type = 'user' 
                            ORDER BY ch2.created_at ASC 
                            LIMIT 1), 
                           'New Chat'
                       ) as title
                FROM conversation c
                LEFT JOIN chat_history ch ON c.id = ch.conversation_id
                WHERE {where_clause}
                GROUP BY c.id, c.user_id, c.started_at, c.last_activity
                {order_clause}
                LIMIT %s OFFSET %s
            """
            
            query_params.extend([params.page_size, offset])
            sessions_data = self.db_service.execute_query(sessions_query, tuple(query_params))
            
            # Convert to response models
            sessions = []
            for session_data in sessions_data:
                # Truncate title if it's too long
                title = session_data['title']
                if len(title) > 50:
                    title = title[:47] + "..."
                
                sessions.append(ChatSessionResponse(
                    id=session_data['id'],
                    user_id=session_data['user_id'],
                    title=title,
                    started_at=session_data['started_at'],
                    last_activity=session_data['last_activity'],
                    message_count=session_data['message_count']
                ))
            
            return ChatSessionListResponse(
                sessions=sessions,
                total_count=total_count,
                page=params.page,
                page_size=params.page_size,
                total_pages=total_pages
            )
            
        except Exception as e:
            logger.error(f"Error getting sessions for user {user_id}: {e}")
            raise
    
    def get_session_messages(self, user_id: int, session_id: int) -> ChatSessionWithMessagesResponse:
        """Get a chat session with all its messages"""
        try:
            # First verify the session belongs to the user
            session_query = """
                SELECT id, user_id, started_at, last_activity
                FROM conversation
                WHERE id = %s AND user_id = %s
            """
            session_data = self.db_service.execute_single_query(session_query, (session_id, user_id))
            
            if not session_data:
                raise ValueError(f"Session {session_id} not found or access denied")
            
            # Get messages for the session
            messages_query = """
                SELECT id, conversation_id, message_type, content_type, content,
                       sql_query, table_data, chart_data, metadata, is_hidden,
                       parent_message_id, created_at, topic_id
                FROM chat_history
                WHERE conversation_id = %s
                ORDER BY created_at ASC
            """
            messages_data = self.db_service.execute_query(messages_query, (session_id,))
            
            # Convert to response models
            messages = []
            for msg_data in messages_data:
                messages.append(ChatMessageResponse(
                    id=msg_data['id'],
                    conversation_id=msg_data['conversation_id'],
                    message_type=msg_data['message_type'],
                    content_type=msg_data['content_type'],
                    content=msg_data['content'],
                    sql_query=msg_data['sql_query'],
                    table_data=msg_data['table_data'],
                    chart_data=msg_data['chart_data'],
                    metadata=msg_data['metadata'] or {},
                    is_hidden=msg_data['is_hidden'],
                    parent_message_id=msg_data['parent_message_id'],
                    created_at=msg_data['created_at'],
                    topic_id=msg_data['topic_id']
                ))
            
            # Generate session title from first user message or default
            title = "New Chat"
            for msg in messages:
                if msg.message_type == "user":
                    title = msg.content[:50] + ("..." if len(msg.content) > 50 else "")
                    break
            
            session = ChatSessionResponse(
                id=session_data['id'],
                user_id=session_data['user_id'],
                title=title,
                started_at=session_data['started_at'],
                last_activity=session_data['last_activity'],
                message_count=len(messages)
            )
            
            return ChatSessionWithMessagesResponse(
                session=session,
                messages=messages
            )
            
        except Exception as e:
            logger.error(f"Error getting session {session_id} for user {user_id}: {e}")
            raise
    
    def add_message(self, user_id: int, session_id: int, request: CreateMessageRequest) -> ChatMessageResponse:
        """Add a new message to a chat session"""
        try:
            # Verify session belongs to user
            session_query = "SELECT id FROM conversation WHERE id = %s AND user_id = %s"
            session_data = self.db_service.execute_single_query(session_query, (session_id, user_id))
            
            if not session_data:
                raise ValueError(f"Session {session_id} not found or access denied")
            
            # Insert message
            message_query = """
                INSERT INTO chat_history (user_id, conversation_id, message_type, content_type,
                                        content, sql_query, table_data, chart_data, metadata,
                                        is_hidden, parent_message_id, created_at, topic_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s)
                RETURNING id, created_at
            """
            
            with self.db_service.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(message_query, (
                        user_id, session_id, request.message_type, request.content_type,
                        request.content, request.sql_query, request.table_data,
                        request.chart_data, request.metadata, request.is_hidden,
                        request.parent_message_id, request.topic_id
                    ))
                    result = cursor.fetchone()
                    message_id = result['id']
                    created_at = result['created_at']
                    
                    # Update session last_activity
                    update_session_query = """
                        UPDATE conversation 
                        SET last_activity = NOW() 
                        WHERE id = %s
                    """
                    cursor.execute(update_session_query, (session_id,))
                    
                    conn.commit()
            
            return ChatMessageResponse(
                id=message_id,
                conversation_id=session_id,
                message_type=request.message_type,
                content_type=request.content_type,
                content=request.content,
                sql_query=request.sql_query,
                table_data=request.table_data,
                chart_data=request.chart_data,
                metadata=request.metadata or {},
                is_hidden=request.is_hidden,
                parent_message_id=request.parent_message_id,
                created_at=created_at,
                topic_id=request.topic_id
            )
            
        except Exception as e:
            logger.error(f"Error adding message to session {session_id} for user {user_id}: {e}")
            raise
    
    def update_session(self, user_id: int, session_id: int, request: UpdateChatSessionRequest) -> ChatSessionResponse:
        """Update a chat session's metadata"""
        try:
            # Verify session belongs to user and get current data
            session_query = """
                SELECT id, user_id, started_at, last_activity
                FROM conversation
                WHERE id = %s AND user_id = %s
            """
            session_data = self.db_service.execute_single_query(session_query, (session_id, user_id))

            if not session_data:
                raise ValueError(f"Session {session_id} not found or access denied")

            # For now, we don't store session titles in the database
            # This is a placeholder for future enhancement
            # The title update would require adding a title column to the conversation table

            # Update last_activity timestamp
            update_query = """
                UPDATE conversation
                SET last_activity = NOW()
                WHERE id = %s AND user_id = %s
                RETURNING last_activity
            """
            result = self.db_service.execute_single_query(update_query, (session_id, user_id))

            # Get message count
            count_query = "SELECT COUNT(*) as count FROM chat_history WHERE conversation_id = %s"
            count_result = self.db_service.execute_single_query(count_query, (session_id,))
            message_count = count_result['count'] if count_result else 0

            # Generate title from first user message or use provided title
            title = request.title or "New Chat"
            if not request.title:
                title_query = """
                    SELECT content
                    FROM chat_history
                    WHERE conversation_id = %s AND message_type = 'user'
                    ORDER BY created_at ASC
                    LIMIT 1
                """
                title_result = self.db_service.execute_single_query(title_query, (session_id,))
                if title_result:
                    title = title_result['content'][:50] + ("..." if len(title_result['content']) > 50 else "")

            return ChatSessionResponse(
                id=session_data['id'],
                user_id=session_data['user_id'],
                title=title,
                started_at=session_data['started_at'],
                last_activity=result['last_activity'],
                message_count=message_count
            )

        except Exception as e:
            logger.error(f"Error updating session {session_id} for user {user_id}: {e}")
            raise

    def delete_session(self, user_id: int, session_id: int) -> bool:
        """Delete a chat session and all its messages"""
        try:
            # Verify session belongs to user
            session_query = "SELECT id FROM conversation WHERE id = %s AND user_id = %s"
            session_data = self.db_service.execute_single_query(session_query, (session_id, user_id))

            if not session_data:
                raise ValueError(f"Session {session_id} not found or access denied")

            # Delete messages and conversation (messages will be deleted by CASCADE)
            with self.db_service.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Delete conversation (messages will cascade)
                    delete_query = "DELETE FROM conversation WHERE id = %s AND user_id = %s"
                    cursor.execute(delete_query, (session_id, user_id))
                    rows_affected = cursor.rowcount

                    conn.commit()

                    return rows_affected > 0

        except Exception as e:
            logger.error(f"Error deleting session {session_id} for user {user_id}: {e}")
            raise

    def get_session_by_id(self, user_id: int, session_id: int) -> Optional[ChatSessionResponse]:
        """Get a specific chat session by ID"""
        try:
            session_query = """
                SELECT c.id, c.user_id, c.started_at, c.last_activity,
                       COUNT(ch.id) as message_count,
                       COALESCE(
                           (SELECT ch2.content
                            FROM chat_history ch2
                            WHERE ch2.conversation_id = c.id
                              AND ch2.message_type = 'user'
                            ORDER BY ch2.created_at ASC
                            LIMIT 1),
                           'New Chat'
                       ) as title
                FROM conversation c
                LEFT JOIN chat_history ch ON c.id = ch.conversation_id
                WHERE c.id = %s AND c.user_id = %s
                GROUP BY c.id, c.user_id, c.started_at, c.last_activity
            """

            session_data = self.db_service.execute_single_query(session_query, (session_id, user_id))

            if not session_data:
                return None

            # Truncate title if it's too long
            title = session_data['title']
            if len(title) > 50:
                title = title[:47] + "..."

            return ChatSessionResponse(
                id=session_data['id'],
                user_id=session_data['user_id'],
                title=title,
                started_at=session_data['started_at'],
                last_activity=session_data['last_activity'],
                message_count=session_data['message_count']
            )

        except Exception as e:
            logger.error(f"Error getting session {session_id} for user {user_id}: {e}")
            raise

    def _get_user_session_count(self, user_id: int) -> int:
        """Get the number of sessions for a user"""
        query = "SELECT COUNT(*) as count FROM conversation WHERE user_id = %s"
        result = self.db_service.execute_single_query(query, (user_id,))
        return result['count'] if result else 0


# Global instance
_chat_service = None

def get_chat_service() -> ChatSessionService:
    """Get the global chat service instance"""
    global _chat_service
    if _chat_service is None:
        _chat_service = ChatSessionService()
    return _chat_service
