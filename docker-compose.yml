services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    restart: unless-stopped
    
  hospital-db:
    image: postgres:15-alpine
    container_name: hospital-db
    environment:
      POSTGRES_DB: ${HOSPITAL_DB_NAME}
      POSTGRES_USER: ${HOSPITAL_DB_USER}
      POSTGRES_PASSWORD: ${HOSPITAL_DB_PASSWORD}
    ports:
      - "5433:5432"
    volumes:
      - hospital-db-data:/var/lib/postgresql/data
    restart: unless-stopped

  app-db:
    image: postgres:15-alpine
    container_name: app-db
    environment:
      POSTGRES_DB: ${APP_DB_NAME}
      POSTGRES_USER: ${APP_DB_USER}
      POSTGRES_PASSWORD: ${APP_DB_PASSWORD}
    ports:
      - "5434:5432"
    volumes:
      - app-db-data:/var/lib/postgresql/data
    restart: unless-stopped

  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: backend
    env_file:
      - .env
    environment:
      # Switch for local/remote DBs
      USE_LOCAL_DB: ${USE_LOCAL_DB}
      # Hospital DB
      HOSPITAL_DB_HOST: ${HOSPITAL_DB_HOST}
      HOSPITAL_DB_PORT: ${HOSPITAL_DB_PORT}
      HOSPITAL_DB_NAME: ${HOSPITAL_DB_NAME}
      HOSPITAL_DB_USER: ${HOSPITAL_DB_USER}
      HOSPITAL_DB_PASSWORD: ${HOSPITAL_DB_PASSWORD}
      # App DB
      APP_DB_HOST: ${APP_DB_HOST}
      APP_DB_PORT: ${APP_DB_PORT}
      APP_DB_NAME: ${APP_DB_NAME}
      APP_DB_USER: ${APP_DB_USER}
      APP_DB_PASSWORD: ${APP_DB_PASSWORD}
      # Ollama
      OLLAMA_MODEL: ${OLLAMA_MODEL}
      OLLAMA_HOST: ${OLLAMA_HOST}
      OLLAMA_PORT: ${OLLAMA_PORT}
      # Other config
      SECRET_KEY: ${SECRET_KEY}
      DEBUG: ${DEBUG}
      CACHE_DIR: ${CACHE_DIR}
    depends_on:
      - hospital-db
      - app-db
      - ollama
    ports:
      - "8000:8000"
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: frontend
    depends_on:
      - backend
    ports:
      - "3000:80"
    restart: unless-stopped

volumes:
  hospital-db-data:
  app-db-data:
  ollama-data:
