from .main_routes import router as main_router
from .sql_routes import router as sql_router
from .analysis_routes import router as analysis_router
from .dashboard_routes import router as dashboard_router
from .chat_routes import router as chat_router

# Keep the old function for compatibility but it's not used in FastAPI
def register_api_blueprints(app):
    """Legacy function - not used in FastAPI"""
    pass

__all__ = ['main_router', 'sql_router', 'analysis_router', 'dashboard_router', 'chat_router']
