# Example .env for AI Hospital Chatbot
# Copy to .env and fill in your secrets/configs

# === General ===
SECRET_KEY=your_secret_key_here
DEBUG=true
CACHE_DIR=persistent_cache

# === Database Switch ===
# Set to 'true' to use local Docker Postgres containers, 'false' to use remote DBs
USE_LOCAL_DB=true

# === Hospital Database (for queries) ===
HOSPITAL_DB_HOST=hospital-db  # Use 'hospital-db' for local Docker, or remote host
HOSPITAL_DB_PORT=5432         # 5432 for Postgres default, 5433 if mapping externally
HOSPITAL_DB_NAME=postgres
HOSPITAL_DB_USER=postgres
HOSPITAL_DB_PASSWORD=your_hospital_db_password

# === App Database (for auth/app data) ===
APP_DB_HOST=app-db            # Use 'app-db' for local Docker, or remote host
APP_DB_PORT=5432              # 5432 for Postgres default, 5434 if mapping externally
APP_DB_NAME=postgres
APP_DB_USER=postgres
APP_DB_PASSWORD=your_app_db_password

# === Ollama LLM ===
OLLAMA_MODEL=sam860/qwen3:4b-Q4_K_XL
OLLAMA_HOST=ollama           # Use 'ollama' for Docker Compose deployment
OLLAMA_PORT=11434

# === Dataframe Context ===
USE_FULL_DATAFRAME_CONTEXT=true
MAX_CONTEXT_ROWS=1000
MAX_CONTEXT_COLUMNS=50

