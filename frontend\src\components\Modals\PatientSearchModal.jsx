import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import LoadingSpinner from '../UI/LoadingSpinner';
import PatientDetailsModal from './PatientDetailsModal';
import authFetch from '../../utils/authFetch';

const PatientSearchModal = () => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('name');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedPatientId, setSelectedPatientId] = useState(null);
  const [showPatientDetails, setShowPatientDetails] = useState(false);

  const handleSearch = async (e) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) {
      setError(t('modals.patientSearch.error.emptyQuery', { defaultValue: 'Please enter a search term' }));
      return;
    }

    setLoading(true);
    setError('');
    setHasSearched(true);

    try {
      const response = await authFetch('/api/v1/search/patients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: searchQuery.trim(),
          type: searchType
        })
      });

      const data = await response.json();

      if (response.ok) {
        setResults(data.results || []);
      } else {
        setError(data.error || t('modals.patientSearch.error.searchFailed', { defaultValue: 'Search failed. Please try again.' }));
        setResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setError(t('errors.network', { defaultValue: 'Network error. Please check your connection and try again.' }));
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return t('modals.patientSearch.table.na', { defaultValue: 'N/A' });
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handlePatientClick = (patient) => {
    setSelectedPatientId(patient.patient_id);
    setShowPatientDetails(true);
  };

  const handleClosePatientDetails = () => {
    setShowPatientDetails(false);
    setSelectedPatientId(null);
  };

  return (
    <div>
      {/* Search Form */}
      <div className="mb-8">
        <form onSubmit={handleSearch} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Search Type */}
            <div>
              <label className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-3">
                {t('modals.patientSearch.searchBy', { defaultValue: 'Search By' })}
              </label>
              <select
                value={searchType}
                onChange={(e) => setSearchType(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-base"
              >
                <option value="name">{t('modals.patientSearch.searchType.name', { defaultValue: 'Patient Name' })}</option>
                <option value="id">{t('modals.patientSearch.searchType.id', { defaultValue: 'Patient ID' })}</option>
                <option value="phone">{t('modals.patientSearch.searchType.phone', { defaultValue: 'Phone Number' })}</option>
              </select>
            </div>

            {/* Search Query */}
            <div>
              <label className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-3">
                {t('modals.patientSearch.searchTerm', { defaultValue: 'Search Term' })}
              </label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={
                  searchType === 'name'
                    ? t('modals.patientSearch.placeholder.name', { defaultValue: 'Enter patient name...' })
                    : searchType === 'id'
                    ? t('modals.patientSearch.placeholder.id', { defaultValue: 'Enter patient ID...' })
                    : t('modals.patientSearch.placeholder.phone', { defaultValue: 'Enter phone number...' })
                }
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-base"
              />
            </div>
          </div>

          {/* Search Button */}
          <div className="flex justify-center">
            <button
              type="submit"
              disabled={loading}
              className="px-8 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold rounded-lg transition-all duration-200 flex items-center text-base shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              aria-label={t('modals.patientSearch.buttons.search', { defaultValue: 'Search Patients' })}
            >
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-3"></i>
                  {t('modals.patientSearch.buttons.searching', { defaultValue: 'Searching...' })}
                </>
              ) : (
                <>
                  <i className="fas fa-search mr-3"></i>
                  {t('modals.patientSearch.buttons.search', { defaultValue: 'Search Patients' })}
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <i className="fas fa-exclamation-triangle text-red-500 mr-3 text-base" aria-label={t('modals.patientSearch.error.icon', { defaultValue: 'Error' })}></i>
            <p className="text-base text-red-700 dark:text-red-400">{error}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" text={t('modals.patientSearch.loading', { defaultValue: 'Searching patients...' })} />
        </div>
      )}

      {/* Results */}
      {!loading && hasSearched && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {t('modals.patientSearch.results.title', { defaultValue: 'Search Results' })}
              {results.length > 0 && (
                <span className="ml-3 text-base font-normal text-gray-500 dark:text-gray-400">
                  {t('modals.patientSearch.results.count', {
                    count: results.length,
                    defaultValue: '({{count}} patient found)',
                    defaultValue_other: '({{count}} patients found)'
                  })}
                </span>
              )}
            </h3>
          </div>

          {results.length === 0 ? (
            <div className="p-12 text-center">
              <i className="fas fa-search text-4xl text-gray-400 mb-4" aria-label={t('modals.patientSearch.results.noPatientsIcon', { defaultValue: 'No patients found' })}></i>
              <p className="text-lg text-gray-500 dark:text-gray-400 mb-2">
                {t('modals.patientSearch.results.noPatients', { defaultValue: 'No patients found' })}
              </p>
              <p className="text-base text-gray-400 dark:text-gray-500">
                {t('modals.patientSearch.results.noPatientsHelp', { defaultValue: 'Try adjusting your search criteria or search terms' })}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto rounded-lg">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.patientSearch.table.patientId', { defaultValue: 'Patient ID' })}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.patientSearch.table.name', { defaultValue: 'Name' })}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.patientSearch.table.birthDate', { defaultValue: 'Birth Date' })}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.patientSearch.table.gender', { defaultValue: 'Gender' })}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.patientSearch.table.contact', { defaultValue: 'Contact' })}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {results.map((patient, index) => (
                    <tr
                      key={index}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                      onClick={() => handlePatientClick(patient)}
                      title={t('modals.patientSearch.table.rowTooltip', { defaultValue: 'Click to view patient details' })}
                    >
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                        {patient.patient_id || t('modals.patientSearch.table.na', { defaultValue: 'N/A' })}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div>
                          <div className="font-medium">{patient.first_name} {patient.last_name}</div>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {formatDate(patient.date_of_birth)}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          patient.gender === 'M'
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                            : patient.gender === 'F'
                            ? 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}>
                          {patient.gender === 'M'
                            ? t('modals.patientSearch.table.genderMale', { defaultValue: 'Male' })
                            : patient.gender === 'F'
                            ? t('modals.patientSearch.table.genderFemale', { defaultValue: 'Female' })
                            : t('modals.patientSearch.table.na', { defaultValue: 'N/A' })}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                        <div>
                          {patient.phone && (
                            <div className="text-xs">
                              <i className="fas fa-phone mr-1" aria-label={t('modals.patientSearch.table.phoneIcon', { defaultValue: 'Phone' })}></i>
                              {patient.phone}
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Patient Details Modal */}
      <PatientDetailsModal
        isOpen={showPatientDetails}
        onClose={handleClosePatientDetails}
        patientId={selectedPatientId}
      />
    </div>
  );
};

export default PatientSearchModal;
