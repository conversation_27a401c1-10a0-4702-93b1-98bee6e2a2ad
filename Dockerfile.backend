# Backend Dockerfile for AI Hospital Chatbot
FROM python:3.11-slim

# Set workdir
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y build-essential libpq-dev && rm -rf /var/lib/apt/lists/*

# Copy requirements and install
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy backend code
COPY backend/ ./backend/
COPY pyproject.toml ./
COPY uv.lock ./

# Copy root-level config files if needed
COPY .env.example ./

# Expose port
EXPOSE 8000

# Entrypoint
CMD ["python", "backend/main.py"]
