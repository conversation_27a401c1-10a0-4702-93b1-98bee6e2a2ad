"""
Chat Session API Routes

This module provides FastAPI routes for chat session management,
including creating, listing, updating, and deleting chat sessions and messages.
"""

import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse

from app.auth.routes import get_current_user
from app.models import User
from app.models.chat import (
    CreateChatSessionRequest, CreateMessageRequest, UpdateChatSessionRequest,
    ChatSessionResponse, ChatMessageResponse, ChatSessionWithMessagesResponse,
    ChatSessionListResponse, ChatSessionListParams,
    ErrorResponse, SuccessResponse
)
from app.services.chat_service import get_chat_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/sessions", response_model=ChatSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_chat_session(
    request: CreateChatSessionRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Create a new chat session for the authenticated user.
    
    - **title**: Optional custom title for the session
    - **topic_id**: Optional topic ID to associate with the session
    
    Returns the created chat session with a welcome message.
    """
    try:
        chat_service = get_chat_service()
        session = chat_service.create_session(current_user.id, request)
        
        logger.info(f"Created new chat session {session.id} for user {current_user.username}")
        return session
        
    except Exception as e:
        logger.error(f"Error creating chat session for user {current_user.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create chat session: {str(e)}"
        )


@router.get("/sessions", response_model=ChatSessionListResponse)
async def list_chat_sessions(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Number of items per page"),
    sort_by: str = Query("last_activity", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc, desc)"),
    search: str = Query(None, description="Search term for session content"),
    current_user: User = Depends(get_current_user)
):
    """
    List all chat sessions for the authenticated user.
    
    - **page**: Page number (default: 1)
    - **page_size**: Number of items per page (default: 20, max: 100)
    - **sort_by**: Sort field (last_activity, started_at, title, id)
    - **sort_order**: Sort order (asc, desc)
    - **search**: Search term for filtering sessions
    
    Returns paginated list of chat sessions.
    """
    try:
        # Create params object with validation
        params = ChatSessionListParams(
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order,
            search=search
        )
        
        chat_service = get_chat_service()
        sessions = chat_service.get_user_sessions(current_user.id, params)
        
        logger.info(f"Listed {len(sessions.sessions)} sessions for user {current_user.username}")
        return sessions
        
    except ValueError as e:
        logger.warning(f"Invalid parameters for listing sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error listing sessions for user {current_user.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list chat sessions: {str(e)}"
        )


@router.get("/sessions/{session_id}", response_model=ChatSessionResponse)
async def get_chat_session(
    session_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific chat session by ID.
    
    - **session_id**: The ID of the chat session to retrieve
    
    Returns the chat session information.
    """
    try:
        chat_service = get_chat_service()
        session = chat_service.get_session_by_id(current_user.id, session_id)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Chat session {session_id} not found or access denied"
            )
        
        logger.info(f"Retrieved session {session_id} for user {current_user.username}")
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session {session_id} for user {current_user.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get chat session: {str(e)}"
        )


@router.get("/sessions/{session_id}/messages", response_model=ChatSessionWithMessagesResponse)
async def get_session_messages(
    session_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    Get all messages for a specific chat session.
    
    - **session_id**: The ID of the chat session
    
    Returns the chat session with all its messages.
    """
    try:
        chat_service = get_chat_service()
        session_with_messages = chat_service.get_session_messages(current_user.id, session_id)
        
        logger.info(f"Retrieved {len(session_with_messages.messages)} messages for session {session_id}")
        return session_with_messages
        
    except ValueError as e:
        logger.warning(f"Session {session_id} not found for user {current_user.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting messages for session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get session messages: {str(e)}"
        )


@router.post("/sessions/{session_id}/messages", response_model=ChatMessageResponse, status_code=status.HTTP_201_CREATED)
async def add_message_to_session(
    session_id: int,
    request: CreateMessageRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Add a new message to a chat session.
    
    - **session_id**: The ID of the chat session
    - **message_type**: Type of message (user or assistant)
    - **content_type**: Type of content (text, sql, chart, etc.)
    - **content**: Main message content
    - **sql_query**: Optional SQL query
    - **table_data**: Optional table results data
    - **chart_data**: Optional chart/plotly data
    - **metadata**: Optional additional metadata
    - **is_hidden**: Whether message is hidden from UI
    - **parent_message_id**: Optional ID of parent message
    - **topic_id**: Optional topic ID
    
    Returns the created message.
    """
    try:
        chat_service = get_chat_service()
        message = chat_service.add_message(current_user.id, session_id, request)
        
        logger.info(f"Added {request.message_type} message to session {session_id}")
        return message
        
    except ValueError as e:
        logger.warning(f"Session {session_id} not found for user {current_user.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error adding message to session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add message: {str(e)}"
        )


@router.put("/sessions/{session_id}", response_model=ChatSessionResponse)
async def update_chat_session(
    session_id: int,
    request: UpdateChatSessionRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Update a chat session's metadata.
    
    - **session_id**: The ID of the chat session to update
    - **title**: Optional new title for the session
    - **topic_id**: Optional new topic ID
    
    Returns the updated chat session.
    """
    try:
        chat_service = get_chat_service()
        session = chat_service.update_session(current_user.id, session_id, request)
        
        logger.info(f"Updated session {session_id} for user {current_user.username}")
        return session
        
    except ValueError as e:
        logger.warning(f"Session {session_id} not found for user {current_user.username}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update chat session: {str(e)}"
        )


@router.delete("/sessions/{session_id}", response_model=SuccessResponse)
async def delete_chat_session(
    session_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    Delete a chat session and all its messages.
    
    - **session_id**: The ID of the chat session to delete
    
    Returns success confirmation.
    """
    try:
        chat_service = get_chat_service()
        success = chat_service.delete_session(current_user.id, session_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Chat session {session_id} not found or access denied"
            )
        
        logger.info(f"Deleted session {session_id} for user {current_user.username}")
        return SuccessResponse(
            success=True,
            message="Chat session deleted successfully",
            data={"deleted_session_id": session_id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete chat session: {str(e)}"
        )
