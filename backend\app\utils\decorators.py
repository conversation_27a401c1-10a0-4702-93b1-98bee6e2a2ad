import time
import random
import hashlib
import functools
from flask import request, jsonify, current_app
from typing import List, Callable, Any

def cache_results(expiry: int = 300):
    """Result caching decorator"""
    # This is a simple in-memory cache for the decorator.
    # Note: This cache is distinct from the main 'cache' object used for session data.
    decorator_cache = {}
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create a cache key from the function name and arguments
            key_parts = [func.__name__]
            # Consider args and kwargs carefully if they contain unhashable types or are too large
            # For simplicity, converting them to strings here, but might need a more robust approach
            try:
                key_parts.extend([str(arg) for arg in args])
                key_parts.extend([f"{k}:{str(v)}" for k, v in sorted(kwargs.items())]) # str(v) for safety
                cache_key = hashlib.md5(":".join(key_parts).encode()).hexdigest()
            except Exception as e:
                # If key generation fails, bypass cache for this call
                current_app.logger.error(f"Cache key generation failed for {func.__name__}: {e}")
                return func(*args, **kwargs)
            
            # Check if result is in cache and not expired
            if cache_key in decorator_cache:
                result, timestamp = decorator_cache[cache_key]
                if time.time() - timestamp < expiry:
                    return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            decorator_cache[cache_key] = (result, time.time())
            
            # Clean up expired entries occasionally (simple strategy)
            if random.random() < 0.1:  # 10% chance to clean on each call
                now = time.time()
                # Iterate over a copy of keys for safe deletion
                for k_to_check in list(decorator_cache.keys()): 
                    if k_to_check in decorator_cache and now - decorator_cache[k_to_check][1] > expiry:
                        del decorator_cache[k_to_check]
            
            return result
        return wrapper
    return decorator


def requires_cache(fields: List[str]) -> Callable:
    """Decorator that ensures required fields are in cache before executing the function"""
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated(*args, **kwargs):
            from app import cache  # Import here to avoid circular imports
            
            func_name = f.__name__
            current_app.logger.info(f"Decorator @requires_cache (for {func_name}): Entered.")
            
            id = request.args.get('id')
            current_app.logger.info(f"Decorator @requires_cache (for {func_name}): Request ID is '{id}'.")

            if id is None:
                current_app.logger.warning(f"Decorator @requires_cache (for {func_name}): No id provided in request. Returning 400.")
                return jsonify({"type": "error", "error": "No id provided"}), 400
            
            field_values = {}
            for field in fields:
                current_app.logger.info(f"Decorator @requires_cache (for {func_name}): Attempting to get field '{field}' for id '{id}' from cache.")
                cached_value = cache.get(id=id, field=field)
                if cached_value is None:
                    current_app.logger.warning(f"Decorator @requires_cache (for {func_name}): Field '{field}' not found in cache for id '{id}'. Returning 404.")
                    return jsonify({"type": "error", "error": f"No {field} found in cache for id {id}"}), 404
                current_app.logger.info(f"Decorator @requires_cache (for {func_name}): Field '{field}' for id '{id}' retrieved from cache.")
                field_values[field] = cached_value
            
            field_values['id'] = id
            current_app.logger.info(f"Decorator @requires_cache (for {func_name}): All required fields found. Proceeding to call wrapped function.")
            return f(*args, **field_values, **kwargs)
        return decorated
    return decorator
