"""
Pydantic models for chat session management APIs
"""
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class MessageType(str, Enum):
    """Message type enumeration"""
    USER = "user"
    ASSISTANT = "assistant"


class ContentType(str, Enum):
    """Content type enumeration"""
    TEXT = "text"
    SQL = "sql"
    CHART = "chart"
    THINKING = "thinking"
    INTERMEDIATE_SQL = "intermediate_sql"
    ERROR = "error"


# Request Models
class CreateChatSessionRequest(BaseModel):
    """Request model for creating a new chat session"""
    title: Optional[str] = Field(None, description="Optional custom title for the session")
    topic_id: Optional[int] = Field(None, description="Optional topic ID to associate with the session")
    
    class Config:
        json_schema_extra = {
            "example": {
                "title": "Patient Data Analysis",
                "topic_id": 1
            }
        }


class CreateMessageRequest(BaseModel):
    """Request model for creating a new message in a chat session"""
    message_type: MessageType = Field(..., description="Type of message (user or assistant)")
    content_type: ContentType = Field(ContentType.TEXT, description="Type of content")
    content: str = Field(..., description="Main message content")
    sql_query: Optional[str] = Field(None, description="SQL query if applicable")
    table_data: Optional[Dict[str, Any]] = Field(None, description="Table results data")
    chart_data: Optional[Dict[str, Any]] = Field(None, description="Chart/plotly data")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    is_hidden: bool = Field(False, description="Whether message is hidden from UI")
    parent_message_id: Optional[int] = Field(None, description="ID of parent message for linking")
    topic_id: Optional[int] = Field(None, description="Topic ID for categorization")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message_type": "user",
                "content_type": "text",
                "content": "Show me all patients from cardiology department",
                "metadata": {"querySessionId": "abc123"}
            }
        }


class UpdateChatSessionRequest(BaseModel):
    """Request model for updating a chat session"""
    title: Optional[str] = Field(None, description="New title for the session")
    topic_id: Optional[int] = Field(None, description="New topic ID")
    
    class Config:
        json_schema_extra = {
            "example": {
                "title": "Updated Session Title"
            }
        }


# Response Models
class ChatMessageResponse(BaseModel):
    """Response model for a chat message"""
    id: int = Field(..., description="Message ID")
    conversation_id: int = Field(..., description="Conversation ID")
    message_type: MessageType = Field(..., description="Type of message")
    content_type: ContentType = Field(..., description="Type of content")
    content: str = Field(..., description="Main message content")
    sql_query: Optional[str] = Field(None, description="SQL query if applicable")
    table_data: Optional[Dict[str, Any]] = Field(None, description="Table results data")
    chart_data: Optional[Dict[str, Any]] = Field(None, description="Chart/plotly data")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    is_hidden: bool = Field(..., description="Whether message is hidden from UI")
    parent_message_id: Optional[int] = Field(None, description="ID of parent message")
    created_at: datetime = Field(..., description="Message creation timestamp")
    topic_id: Optional[int] = Field(None, description="Topic ID")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "conversation_id": 1,
                "message_type": "user",
                "content_type": "text",
                "content": "Show me all patients from cardiology department",
                "sql_query": None,
                "table_data": None,
                "chart_data": None,
                "metadata": {"querySessionId": "abc123"},
                "is_hidden": False,
                "parent_message_id": None,
                "created_at": "2025-01-09T10:30:00Z",
                "topic_id": 1
            }
        }


class ChatSessionResponse(BaseModel):
    """Response model for a chat session"""
    id: int = Field(..., description="Session ID")
    user_id: int = Field(..., description="User ID who owns the session")
    title: Optional[str] = Field(None, description="Session title")
    started_at: datetime = Field(..., description="Session start timestamp")
    last_activity: datetime = Field(..., description="Last activity timestamp")
    message_count: Optional[int] = Field(None, description="Number of messages in session")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "user_id": 1,
                "title": "Patient Data Analysis",
                "started_at": "2025-01-09T10:00:00Z",
                "last_activity": "2025-01-09T10:30:00Z",
                "message_count": 5
            }
        }


class ChatSessionWithMessagesResponse(BaseModel):
    """Response model for a chat session with its messages"""
    session: ChatSessionResponse = Field(..., description="Session information")
    messages: List[ChatMessageResponse] = Field(..., description="List of messages in the session")
    
    class Config:
        json_schema_extra = {
            "example": {
                "session": {
                    "id": 1,
                    "user_id": 1,
                    "title": "Patient Data Analysis",
                    "started_at": "2025-01-09T10:00:00Z",
                    "last_activity": "2025-01-09T10:30:00Z",
                    "message_count": 2
                },
                "messages": [
                    {
                        "id": 1,
                        "conversation_id": 1,
                        "message_type": "user",
                        "content_type": "text",
                        "content": "Show me all patients",
                        "sql_query": None,
                        "table_data": None,
                        "chart_data": None,
                        "metadata": {},
                        "is_hidden": False,
                        "parent_message_id": None,
                        "created_at": "2025-01-09T10:00:00Z",
                        "topic_id": None
                    }
                ]
            }
        }


class ChatSessionListResponse(BaseModel):
    """Response model for listing chat sessions"""
    sessions: List[ChatSessionResponse] = Field(..., description="List of chat sessions")
    total_count: int = Field(..., description="Total number of sessions")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")
    
    class Config:
        json_schema_extra = {
            "example": {
                "sessions": [
                    {
                        "id": 1,
                        "user_id": 1,
                        "title": "Patient Data Analysis",
                        "started_at": "2025-01-09T10:00:00Z",
                        "last_activity": "2025-01-09T10:30:00Z",
                        "message_count": 5
                    }
                ],
                "total_count": 1,
                "page": 1,
                "page_size": 20,
                "total_pages": 1
            }
        }


# Query Parameters Models
class ChatSessionListParams(BaseModel):
    """Query parameters for listing chat sessions"""
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(20, ge=1, le=100, description="Number of items per page")
    sort_by: str = Field("last_activity", description="Sort field (last_activity, started_at, title)")
    sort_order: str = Field("desc", description="Sort order (asc, desc)")
    search: Optional[str] = Field(None, description="Search term for session titles")
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = ['last_activity', 'started_at', 'title', 'id']
        if v not in allowed_fields:
            raise ValueError(f'sort_by must be one of: {allowed_fields}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v.lower() not in ['asc', 'desc']:
            raise ValueError('sort_order must be either "asc" or "desc"')
        return v.lower()


# Error Response Models
class ErrorResponse(BaseModel):
    """Standard error response model"""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    code: Optional[str] = Field(None, description="Error code")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "Session not found",
                "detail": "Chat session with ID 123 does not exist or you don't have permission to access it",
                "code": "SESSION_NOT_FOUND"
            }
        }


# Success Response Models
class SuccessResponse(BaseModel):
    """Standard success response model"""
    success: bool = Field(True, description="Operation success status")
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional response data")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Chat session deleted successfully",
                "data": {"deleted_session_id": 123}
            }
        }
