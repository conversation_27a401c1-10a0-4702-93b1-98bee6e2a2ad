"""
App Database Service

This module provides database connection and query functionality for the app database,
which stores user authentication data, chat history, and other application-specific data.
"""

import logging
import psycopg2
import psycopg2.extras
from psycopg2 import pool
from contextlib import contextmanager
from typing import Optional, Dict, Any, List
import sys
import os

# Add backend directory to path to import config
backend_dir = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, backend_dir)

from config import Config

logger = logging.getLogger(__name__)

class AppDatabaseService:
    """Service for managing app database connections and operations"""
    
    def __init__(self, config: Config = None):
        self.config = config or Config()
        self.connection_pool = None
        self._initialize_connection_pool()
    
    def _initialize_connection_pool(self):
        """Initialize the connection pool for the app database"""
        try:
            app_config = self.config.app_database_config
            self.connection_pool = psycopg2.pool.SimpleConnectionPool(
                minconn=1,
                maxconn=10,
                host=app_config['host'],
                dbname=app_config['dbname'],
                user=app_config['user'],
                password=app_config['password'],
                port=app_config['port'],
                cursor_factory=psycopg2.extras.RealDictCursor
            )
            logger.info("App database connection pool initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize app database connection pool: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Context manager for getting a database connection from the pool"""
        connection = None
        try:
            connection = self.connection_pool.getconn()
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"Database operation failed: {e}")
            raise
        finally:
            if connection:
                self.connection_pool.putconn(connection)
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results as a list of dictionaries"""
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
    
    def execute_single_query(self, query: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """Execute a SELECT query and return a single result as a dictionary"""
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchone()
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute an INSERT/UPDATE/DELETE query and return the number of affected rows"""
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                conn.commit()
                return cursor.rowcount
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username from the app database"""
        query = """
            SELECT u.id, u.username, u.first_name, u.last_name, u.password_hash, 
                   u.is_active, r.name as role_name, d.name as department_name
            FROM "user" u
            JOIN role r ON u.role_id = r.id
            LEFT JOIN department d ON u.department_id = d.id
            WHERE u.username = %s AND u.is_active = true
        """
        return self.execute_single_query(query, (username,))
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by ID from the app database"""
        query = """
            SELECT u.id, u.username, u.first_name, u.last_name, u.password_hash, 
                   u.is_active, r.name as role_name, d.name as department_name
            FROM "user" u
            JOIN role r ON u.role_id = r.id
            LEFT JOIN department d ON u.department_id = d.id
            WHERE u.id = %s AND u.is_active = true
        """
        return self.execute_single_query(query, (user_id,))
    
    def create_user(self, username: str, first_name: str, last_name: str, 
                   password_hash: str, role_id: int, department_id: int = None) -> int:
        """Create a new user in the app database"""
        query = """
            INSERT INTO "user" (username, first_name, last_name, password_hash, 
                              role_id, department_id, is_active, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, true, NOW(), NOW())
            RETURNING id
        """
        with self.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, (username, first_name, last_name, password_hash, role_id, department_id))
                conn.commit()
                return cursor.fetchone()['id']
    
    def update_user_password(self, user_id: int, password_hash: str) -> bool:
        """Update user password"""
        query = """
            UPDATE "user" 
            SET password_hash = %s, updated_at = NOW()
            WHERE id = %s AND is_active = true
        """
        rows_affected = self.execute_update(query, (password_hash, user_id))
        return rows_affected > 0
    
    def get_roles(self) -> List[Dict[str, Any]]:
        """Get all roles from the app database"""
        query = "SELECT id, name, description FROM role ORDER BY name"
        return self.execute_query(query)
    
    def get_departments(self) -> List[Dict[str, Any]]:
        """Get all departments from the app database"""
        query = "SELECT id, name, description FROM department ORDER BY name"
        return self.execute_query(query)
    
    def close(self):
        """Close the connection pool"""
        if self.connection_pool:
            self.connection_pool.closeall()
            logger.info("App database connection pool closed")

# Global instance
_app_db_service = None

def get_app_database_service() -> AppDatabaseService:
    """Get the global app database service instance"""
    global _app_db_service
    if _app_db_service is None:
        _app_db_service = AppDatabaseService()
    return _app_db_service

def close_app_database_service():
    """Close the global app database service"""
    global _app_db_service
    if _app_db_service:
        _app_db_service.close()
        _app_db_service = None
