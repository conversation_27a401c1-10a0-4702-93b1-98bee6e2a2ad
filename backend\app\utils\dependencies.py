"""
FastAPI dependencies to replace Flask decorators
"""
import time
import random
import hashlib
import functools
import logging
from typing import List, Callable, Any, Dict
from fastapi import Request, HTTPException, Depends
import pandas as pd

logger = logging.getLogger(__name__)

# Simple in-memory cache for the decorator
decorator_cache: Dict[str, tuple] = {}

def cache_results(expiry: int = 300):
    """Result caching dependency"""
    def dependency(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Create a cache key from the function name and arguments
            key_parts = [func.__name__]
            try:
                key_parts.extend([str(arg) for arg in args])
                key_parts.extend([f"{k}:{str(v)}" for k, v in sorted(kwargs.items())])
                cache_key = hashlib.md5(":".join(key_parts).encode()).hexdigest()
            except Exception as e:
                logger.error(f"Cache key generation failed for {func.__name__}: {e}")
                return await func(*args, **kwargs)
            
            # Check if result is in cache and not expired
            if cache_key in decorator_cache:
                result, timestamp = decorator_cache[cache_key]
                if time.time() - timestamp < expiry:
                    return result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            decorator_cache[cache_key] = (result, time.time())
            
            # Clean up expired entries occasionally
            if random.random() < 0.1:
                now = time.time()
                for k_to_check in list(decorator_cache.keys()):
                    if k_to_check in decorator_cache and now - decorator_cache[k_to_check][1] > expiry:
                        del decorator_cache[k_to_check]
            
            return result
        return wrapper
    return dependency

class CacheRequirement:
    """Dependency class to ensure required fields are in cache"""
    
    def __init__(self, fields: List[str]):
        self.fields = fields
    
    async def __call__(self, request: Request, id: str = None) -> Dict[str, Any]:
        """Check if required fields are in cache and return them"""
        cache = request.app.state.cache
        
        if id is None:
            raise HTTPException(status_code=400, detail={"type": "error", "error": "No id provided"})
        
        field_values = {}
        for field in self.fields:
            logger.info(f"Attempting to get field '{field}' for id '{id}' from cache.")
            cached_value = cache.get(id=id, field=field)
            if cached_value is None:
                logger.warning(f"Field '{field}' not found in cache for id '{id}'.")
                raise HTTPException(
                    status_code=404, 
                    detail={"type": "error", "error": f"No {field} found in cache for id {id}"}
                )
            logger.info(f"Field '{field}' for id '{id}' retrieved from cache.")
            field_values[field] = cached_value
        
        field_values['id'] = id
        return field_values

def requires_cache(fields: List[str]):
    """Create a dependency that requires specific cache fields"""
    return CacheRequirement(fields)

# Specific dependency instances for common use cases
requires_sql = requires_cache(['sql'])
requires_df_and_question = requires_cache(['df', 'question'])
requires_df_question_sql = requires_cache(['df', 'question', 'sql'])

async def get_vanna(request: Request):
    """Dependency to get Vanna instance"""
    return request.app.state.vn

async def get_cache(request: Request):
    """Dependency to get cache instance"""
    return request.app.state.cache

async def get_config(request: Request):
    """Dependency to get config instance"""
    return request.app.state.config
