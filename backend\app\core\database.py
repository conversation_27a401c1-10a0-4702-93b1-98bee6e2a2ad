from .vanna_custom import <PERSON><PERSON><PERSON>
import sys
import os

# Add the backend directory to the path to import config
backend_dir = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, backend_dir)

from config import Config

def setup_vanna():
    """Initialize and configure <PERSON><PERSON> with database connection and training"""
    config = Config()
    
    vn = MyVanna(config=config.vanna_config)
    
    # Connect to PostgreSQL database
    vn.connect_to_postgres(**config.database_config)
    
    # Check if we already have training data to avoid redundant training
    try:
        existing_training = vn.get_training_data()
        if len(existing_training) > 0:
            print("Training data already exists. Skipping training...")
            return vn
    except Exception as e:
        print(f"Error checking existing training data: {e}. Will proceed with training.")
      # Train on schema
    try:
        df_information_schema = vn.run_sql(
            """
            SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE table_schema = 'public'
              AND table_name IN ('patients', 'medecins', 'consult_prob', 'motifs', 'symptomes', 
                                'observations', 'antecedents', 'biometrie', 'diagnostics', 
                                'examens_comp', 'procedures_soins', 'livret_medicaments', 
                                'medicaments', 'prescriptions')
            """
        )
        plan = vn.get_training_plan_generic(df_information_schema)
        vn.train(plan=plan)
        
        # Train with DDL
        vn.train(ddl="""
            -- Patients table
            CREATE TABLE Patients (
                ID_patient SERIAL PRIMARY KEY,
                Nom VARCHAR(100) NOT NULL,
                Prenom VARCHAR(100),
                DateNaissance DATE,
                Sexe CHAR(1),
                Adresse VARCHAR(255),
                Telephone VARCHAR(20),
                Email VARCHAR(100)
            );

            -- Medecins table
            CREATE TABLE Medecins (
                ID_medecin SERIAL PRIMARY KEY,
                Nom VARCHAR(100) NOT NULL,
                Prenom VARCHAR(100),
                Specialite VARCHAR(100),
                AdressePostale VARCHAR(255),
                Email VARCHAR(100)
            );

            -- Consultations table
            CREATE TABLE Consult_prob (
                ID_probleme SERIAL PRIMARY KEY,
                ID_patient INT NOT NULL,
                ID_medecin INT,
                DateConsultation TIMESTAMP,
                LibelleProbleme VARCHAR(255),
                FOREIGN KEY (ID_patient) REFERENCES Patients(ID_patient),
                FOREIGN KEY (ID_medecin) REFERENCES Medecins(ID_medecin)
            );

            -- Motifs table
            CREATE TABLE Motifs (
                ID_motif SERIAL PRIMARY KEY,
                Libelle VARCHAR(255) NOT NULL,
                ID_probleme INT NOT NULL,
                FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
            );

            -- Symptomes table
            CREATE TABLE Symptomes (
                ID_symptome SERIAL PRIMARY KEY,
                Libelle VARCHAR(255) NOT NULL,
                ID_probleme INT NOT NULL,
                FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
            );

            -- Observations table
            CREATE TABLE Observations (
                ID_observation SERIAL PRIMARY KEY,
                Observation TEXT NOT NULL,
                DateObservation TIMESTAMP,
                ID_probleme INT NOT NULL,
                FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
            );

            -- Antecedents table
            CREATE TABLE Antecedents (
                ID_ATCD SERIAL PRIMARY KEY,
                Libelle VARCHAR(255) NOT NULL,
                Details TEXT,
                ID_patient INT NOT NULL,
                FOREIGN KEY (ID_patient) REFERENCES Patients(ID_patient)
            );

            -- Biometrie table
            CREATE TABLE Biometrie (
                ID_biometrie SERIAL PRIMARY KEY,
                ID_patient INT NOT NULL,
                ID_probleme INT,
                DateMesure TIMESTAMP NOT NULL,
                Temperature DECIMAL(4,1),
                TensionSystolique INT,
                TensionDiastolique INT,
                Pouls INT,
                SpO2 INT,
                FrequenceResp INT,
                Commentaire TEXT,
                FOREIGN KEY (ID_patient) REFERENCES Patients(ID_patient),
                FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
            );

            -- Diagnostics table
            CREATE TABLE Diagnostics (
                ID_diagnostic SERIAL PRIMARY KEY,
                CodeCIM VARCHAR(20),
                Libelle VARCHAR(255) NOT NULL,
                ID_probleme INT NOT NULL,
                FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
            );

            -- Examens complémentaires table
            CREATE TABLE Examens_comp (
                ID_examen SERIAL PRIMARY KEY,
                TypeExamen VARCHAR(100),
                Libelle VARCHAR(255),
                DateExamen TIMESTAMP,
                Resultats TEXT,
                ID_probleme INT NOT NULL,
                FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
            );

            -- Procedures soins table
            CREATE TABLE Procedures_soins (
                ID_procedure SERIAL PRIMARY KEY,
                Libelle VARCHAR(255) NOT NULL,
                TypeProcedure VARCHAR(100),
                DateProcedure TIMESTAMP,
                ID_probleme INT NOT NULL,
                FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme)
            );

            -- Livret medicaments table
            CREATE TABLE Livret_medicaments (
                ID_livret SERIAL PRIMARY KEY,
                CIP13 VARCHAR(20),
                DCI VARCHAR(255),
                NomCommercial VARCHAR(255)
            );

            -- Medicaments table
            CREATE TABLE Medicaments (
                ID_medicament SERIAL PRIMARY KEY,
                ID_livret INT,
                Nom VARCHAR(255) NOT NULL,
                Forme VARCHAR(100),
                Dosage VARCHAR(100),
                FOREIGN KEY (ID_livret) REFERENCES Livret_medicaments(ID_livret)
            );

            -- Prescriptions table
            CREATE TABLE Prescriptions (
                ID_prescription SERIAL PRIMARY KEY,
                ID_probleme INT,
                ID_medicament INT,
                DatePrescription TIMESTAMP,
                FOREIGN KEY (ID_probleme) REFERENCES Consult_prob(ID_probleme),
                FOREIGN KEY (ID_medicament) REFERENCES Medicaments(ID_medicament)
            );
        """)
        
        # Train with documentation
        vn.train(documentation="""
            Medical Database Schema Documentation:
            
            - Patients table: Contains patient information including personal details (name, birth date, gender, address, phone, email)
            - Medecins table: Contains doctor information including specialization and contact details
            - Consult_prob table: Central table for consultation problems linking patients and doctors
            - Motifs table: Consultation motifs/reasons linked to problems
            - Symptomes table: Patient symptoms associated with consultation problems
            - Observations table: Clinical observations made during consultations
            - Antecedents table: Patient medical history and antecedents
            - Biometrie table: Biometric measurements (temperature, blood pressure, pulse, SpO2, respiratory rate)
            - Diagnostics table: Medical diagnoses with ICD codes
            - Examens_comp table: Complementary examinations and their results
            - Procedures_soins table: Medical procedures and care provided
            - Livret_medicaments table: Drug reference database with commercial names and DCI
            - Medicaments table: Medication details including form and dosage
            - Prescriptions table: Medication prescriptions linked to consultation problems
            
            Key relationships:
            - Patients have consultations (Consult_prob) with doctors (Medecins)
            - Each consultation problem can have multiple motifs, symptoms, observations, diagnostics, examinations, and procedures
            - Patients have medical antecedents and biometric measurements
            - Prescriptions link consultation problems with medications
        """)

        # Train with question-SQL pairs for hospital management
        print("Training question-SQL pairs...")

        # Patient-related queries
        vn.train(
            question="What is the average age of our patients?",
            sql="SELECT AVG(EXTRACT(YEAR FROM AGE(CURRENT_DATE, DateNaissance))) as average_age FROM Patients WHERE DateNaissance IS NOT NULL"
        )

        vn.train(
            question="How many patients do we have in total?",
            sql="SELECT COUNT(*) as total_patients FROM Patients"
        )

        vn.train(
            question="Show me all patients born after 1990",
            sql="SELECT Nom, Prenom, DateNaissance FROM Patients WHERE DateNaissance > '1990-01-01' ORDER BY DateNaissance DESC"
        )

        vn.train(
            question="List all female patients",
            sql="SELECT Nom, Prenom, DateNaissance, Telephone FROM Patients WHERE Sexe = 'F' ORDER BY Nom"
        )

        vn.train(
            question="Find patients with email addresses",
            sql="SELECT Nom, Prenom, Email FROM Patients WHERE Email IS NOT NULL AND Email != '' ORDER BY Nom"
        )

        # Doctor-related queries
        vn.train(
            question="How many doctors do we have by specialization?",
            sql="SELECT Specialite, COUNT(*) as nombre_medecins FROM Medecins GROUP BY Specialite ORDER BY nombre_medecins DESC"
        )

        vn.train(
            question="List all cardiologists",
            sql="SELECT Nom, Prenom, Email FROM Medecins WHERE Specialite ILIKE '%cardio%' ORDER BY Nom"
        )

        vn.train(
            question="Show all doctors and their specializations",
            sql="SELECT Nom, Prenom, Specialite FROM Medecins ORDER BY Specialite, Nom"
        )

        # Consultation-related queries
        vn.train(
            question="How many consultations were there this month?",
            sql="SELECT COUNT(*) as consultations_ce_mois FROM Consult_prob WHERE DATE_TRUNC('month', DateConsultation) = DATE_TRUNC('month', CURRENT_DATE)"
        )

        vn.train(
            question="Show recent consultations with patient and doctor names",
            sql="SELECT p.Nom as patient_nom, p.Prenom as patient_prenom, m.Nom as medecin_nom, m.Prenom as medecin_prenom, c.DateConsultation, c.LibelleProbleme FROM Consult_prob c JOIN Patients p ON c.ID_patient = p.ID_patient LEFT JOIN Medecins m ON c.ID_medecin = m.ID_medecin ORDER BY c.DateConsultation DESC LIMIT 20"
        )

        vn.train(
            question="Which doctor has the most consultations?",
            sql="SELECT m.Nom, m.Prenom, m.Specialite, COUNT(c.ID_probleme) as nombre_consultations FROM Medecins m JOIN Consult_prob c ON m.ID_medecin = c.ID_medecin GROUP BY m.ID_medecin, m.Nom, m.Prenom, m.Specialite ORDER BY nombre_consultations DESC LIMIT 1"
        )

        vn.train(
            question="Show consultations from the last 7 days",
            sql="SELECT p.Nom, p.Prenom, c.DateConsultation, c.LibelleProbleme FROM Consult_prob c JOIN Patients p ON c.ID_patient = p.ID_patient WHERE c.DateConsultation >= CURRENT_DATE - INTERVAL '7 days' ORDER BY c.DateConsultation DESC"
        )

        # Symptom and diagnosis queries
        vn.train(
            question="What are the most common symptoms?",
            sql="SELECT s.Libelle, COUNT(*) as frequency FROM Symptomes s GROUP BY s.Libelle ORDER BY frequency DESC LIMIT 10"
        )

        vn.train(
            question="Show all symptoms for a specific patient",
            sql="SELECT p.Nom, p.Prenom, s.Libelle as symptome, c.DateConsultation FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Symptomes s ON c.ID_probleme = s.ID_probleme WHERE p.ID_patient = ? ORDER BY c.DateConsultation DESC"
        )

        vn.train(
            question="What are the most common diagnoses?",
            sql="SELECT d.Libelle, COUNT(*) as frequency FROM Diagnostics d GROUP BY d.Libelle ORDER BY frequency DESC LIMIT 10"
        )

        vn.train(
            question="Show patients with diabetes diagnosis",
            sql="SELECT DISTINCT p.Nom, p.Prenom, d.Libelle, c.DateConsultation FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Diagnostics d ON c.ID_probleme = d.ID_probleme WHERE d.Libelle ILIKE (use intermediate sql to explore the data have in the databases) OR d.Libelle ILIKE (use intermediate sql to explore the data have in the databases) ORDER BY c.DateConsultation DESC"
        )

        # Biometric data queries
        vn.train(
            question="Show average vital signs for all patients",
            sql="SELECT AVG(Temperature) as avg_temperature, AVG(TensionSystolique) as avg_systolic, AVG(TensionDiastolique) as avg_diastolic, AVG(Pouls) as avg_pulse, AVG(SpO2) as avg_spo2 FROM Biometrie WHERE Temperature IS NOT NULL"
        )

        vn.train(
            question="Find patients with high blood pressure",
            sql="SELECT DISTINCT p.Nom, p.Prenom, b.TensionSystolique, b.TensionDiastolique, b.DateMesure FROM Patients p JOIN Biometrie b ON p.ID_patient = b.ID_patient WHERE b.TensionSystolique > 140 OR b.TensionDiastolique > 90 ORDER BY b.DateMesure DESC"
        )

        vn.train(
            question="Show latest biometric measurements for each patient",
            sql="SELECT p.Nom, p.Prenom, b.Temperature, b.TensionSystolique, b.TensionDiastolique, b.Pouls, b.SpO2, b.DateMesure FROM Patients p JOIN (SELECT ID_patient, MAX(DateMesure) as latest_date FROM Biometrie GROUP BY ID_patient) latest ON p.ID_patient = latest.ID_patient JOIN Biometrie b ON p.ID_patient = b.ID_patient AND b.DateMesure = latest.latest_date ORDER BY p.Nom"
        )

        # Medication and prescription queries
        vn.train(
            question="What are the most prescribed medications?",
            sql="SELECT m.Nom, COUNT(*) as prescription_count FROM Medicaments m JOIN Prescriptions pr ON m.ID_medicament = pr.ID_medicament GROUP BY m.ID_medicament, m.Nom ORDER BY prescription_count DESC LIMIT 10"
        )

        vn.train(
            question="Show all prescriptions for a patient",
            sql="SELECT p.Nom, p.Prenom, m.Nom as medicament, pr.DatePrescription FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Prescriptions pr ON c.ID_probleme = pr.ID_probleme JOIN Medicaments m ON pr.ID_medicament = m.ID_medicament WHERE p.ID_patient = ? ORDER BY pr.DatePrescription DESC"
        )

        vn.train(
            question="List all available medications",
            sql="SELECT m.Nom, m.Forme, m.Dosage, lm.NomCommercial FROM Medicaments m LEFT JOIN Livret_medicaments lm ON m.ID_livret = lm.ID_livret ORDER BY m.Nom"
        )

        # Medical history queries
        vn.train(
            question="Show patients with specific medical antecedents",
            sql="SELECT p.Nom, p.Prenom, a.Libelle, a.Details FROM Patients p JOIN Antecedents a ON p.ID_patient = a.ID_patient WHERE a.Libelle ILIKE (use intermediate sql to explore the data have in the databases) OR a.Libelle ILIKE (use intermediate sql to explore the data have in the databases) ORDER BY p.Nom"
        )

        vn.train(
            question="Count patients by medical antecedents",
            sql="SELECT a.Libelle, COUNT(DISTINCT a.ID_patient) as patient_count FROM Antecedents a GROUP BY a.Libelle ORDER BY patient_count DESC"
        )

        # Examination queries
        vn.train(
            question="Show recent examination results",
            sql="SELECT p.Nom, p.Prenom, e.TypeExamen, e.Libelle, e.DateExamen, e.Resultats FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Examens_comp e ON c.ID_probleme = e.ID_probleme WHERE e.DateExamen >= CURRENT_DATE - INTERVAL '30 days' ORDER BY e.DateExamen DESC"
        )

        vn.train(
            question="What types of examinations are most common?",
            sql="SELECT TypeExamen, COUNT(*) as exam_count FROM Examens_comp GROUP BY TypeExamen ORDER BY exam_count DESC"
        )

        # Procedure queries
        vn.train(
            question="Show recent medical procedures",
            sql="SELECT p.Nom, p.Prenom, ps.Libelle, ps.TypeProcedure, ps.DateProcedure FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Procedures_soins ps ON c.ID_probleme = ps.ID_probleme WHERE ps.DateProcedure >= CURRENT_DATE - INTERVAL '30 days' ORDER BY ps.DateProcedure DESC"
        )

        vn.train(
            question="Count procedures by type",
            sql="SELECT TypeProcedure, COUNT(*) as procedure_count FROM Procedures_soins GROUP BY TypeProcedure ORDER BY procedure_count DESC"
        )

        # Complex analytical queries
        vn.train(
            question="Show patient consultation frequency over the last 6 months",
            sql="SELECT DATE_TRUNC('month', c.DateConsultation) as month, COUNT(*) as consultation_count FROM Consult_prob c WHERE c.DateConsultation >= CURRENT_DATE - INTERVAL '6 months' GROUP BY DATE_TRUNC('month', c.DateConsultation) ORDER BY month"
        )

        vn.train(
            question="Find patients who haven't had a consultation in the last year",
            sql="SELECT p.Nom, p.Prenom, p.Telephone, MAX(c.DateConsultation) as last_consultation FROM Patients p LEFT JOIN Consult_prob c ON p.ID_patient = c.ID_patient GROUP BY p.ID_patient, p.Nom, p.Prenom, p.Telephone HAVING MAX(c.DateConsultation) < CURRENT_DATE - INTERVAL '1 year' OR MAX(c.DateConsultation) IS NULL ORDER BY last_consultation"
        )

        vn.train(
            question="Show doctor workload by number of patients treated",
            sql="SELECT m.Nom, m.Prenom, m.Specialite, COUNT(DISTINCT c.ID_patient) as unique_patients, COUNT(c.ID_probleme) as total_consultations FROM Medecins m LEFT JOIN Consult_prob c ON m.ID_medecin = c.ID_medecin GROUP BY m.ID_medecin, m.Nom, m.Prenom, m.Specialite ORDER BY unique_patients DESC"
        )

        vn.train(
            question="Find patients with multiple chronic conditions",
            sql="SELECT p.Nom, p.Prenom, COUNT(DISTINCT a.Libelle) as condition_count, STRING_AGG(DISTINCT a.Libelle, ', ') as conditions FROM Patients p JOIN Antecedents a ON p.ID_patient = a.ID_patient WHERE a.Libelle ILIKE ANY(ARRAY(use intermediate sql to explore the data have in the databases)) GROUP BY p.ID_patient, p.Nom, p.Prenom HAVING COUNT(DISTINCT a.Libelle) > 1 ORDER BY condition_count DESC"
        )

        vn.train(
            question="Show medication adherence by tracking prescription frequency",
            sql="SELECT m.Nom as medication, COUNT(*) as prescription_frequency, COUNT(DISTINCT pr.ID_probleme) as unique_cases, AVG(EXTRACT(DAY FROM (pr.DatePrescription - LAG(pr.DatePrescription) OVER (PARTITION BY m.ID_medicament ORDER BY pr.DatePrescription)))) as avg_days_between FROM Medicaments m JOIN Prescriptions pr ON m.ID_medicament = pr.ID_medicament GROUP BY m.ID_medicament, m.Nom HAVING COUNT(*) > 5 ORDER BY prescription_frequency DESC"
        )

        vn.train(
            question="Identify patients at risk based on vital signs trends",
            sql="SELECT p.Nom, p.Prenom, AVG(b.TensionSystolique) as avg_systolic, AVG(b.TensionDiastolique) as avg_diastolic, COUNT(b.ID_biometrie) as measurement_count FROM Patients p JOIN Biometrie b ON p.ID_patient = b.ID_patient WHERE b.DateMesure >= CURRENT_DATE - INTERVAL '3 months' GROUP BY p.ID_patient, p.Nom, p.Prenom HAVING AVG(b.TensionSystolique) > 140 OR AVG(b.TensionDiastolique) > 90 ORDER BY avg_systolic DESC"
        )

        vn.train(
            question="Show seasonal patterns in consultations",
            sql="SELECT EXTRACT(MONTH FROM DateConsultation) as month, CASE WHEN EXTRACT(MONTH FROM DateConsultation) IN (12,1,2) THEN 'Winter' WHEN EXTRACT(MONTH FROM DateConsultation) IN (3,4,5) THEN 'Spring' WHEN EXTRACT(MONTH FROM DateConsultation) IN (6,7,8) THEN 'Summer' ELSE 'Fall' END as season, COUNT(*) as consultation_count FROM Consult_prob WHERE DateConsultation >= CURRENT_DATE - INTERVAL '2 years' GROUP BY EXTRACT(MONTH FROM DateConsultation), season ORDER BY month"
        )

        vn.train(
            question="Find the most effective treatments by tracking patient outcomes",
            sql="SELECT m.Nom as medication, COUNT(DISTINCT pr.ID_probleme) as cases_treated, AVG(CASE WHEN follow_up.DateConsultation IS NULL THEN 1 ELSE 0 END) as resolution_rate FROM Medicaments m JOIN Prescriptions pr ON m.ID_medicament = pr.ID_medicament JOIN Consult_prob c ON pr.ID_probleme = c.ID_probleme LEFT JOIN Consult_prob follow_up ON c.ID_patient = follow_up.ID_patient AND follow_up.DateConsultation > c.DateConsultation AND follow_up.DateConsultation <= c.DateConsultation + INTERVAL '30 days' GROUP BY m.ID_medicament, m.Nom HAVING COUNT(DISTINCT pr.ID_probleme) >= 5 ORDER BY resolution_rate DESC"
        )

        # Age-based analysis queries
        vn.train(
            question="Show patient distribution by age groups",
            sql="SELECT CASE WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, DateNaissance)) < 18 THEN 'Under 18' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, DateNaissance)) BETWEEN 18 AND 30 THEN '18-30' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, DateNaissance)) BETWEEN 31 AND 50 THEN '31-50' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, DateNaissance)) BETWEEN 51 AND 70 THEN '51-70' ELSE 'Over 70' END as age_group, COUNT(*) as patient_count FROM Patients WHERE DateNaissance IS NOT NULL GROUP BY age_group ORDER BY age_group"
        )

        vn.train(
            question="Find common health issues by age group",
            sql="SELECT CASE WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) < 30 THEN 'Young Adults' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) BETWEEN 30 AND 60 THEN 'Middle Age' ELSE 'Seniors' END as age_group, d.Libelle, COUNT(*) as frequency FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Diagnostics d ON c.ID_probleme = d.ID_probleme WHERE p.DateNaissance IS NOT NULL GROUP BY age_group, d.Libelle ORDER BY age_group, frequency DESC"
        )

        # Emergency and urgent care queries
        vn.train(
            question="Identify urgent cases based on symptoms",
            sql="SELECT p.Nom, p.Prenom, c.DateConsultation, s.Libelle as symptom, c.LibelleProbleme FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Symptomes s ON c.ID_probleme = s.ID_probleme WHERE s.Libelle ILIKE ANY(ARRAY[(use intermediate sql to explore the data have in the databases)]) ORDER BY c.DateConsultation DESC"
        )

        vn.train(
            question="Show patients requiring immediate follow-up",
            sql="SELECT p.Nom, p.Prenom, c.DateConsultation, d.Libelle as diagnosis, o.Observation FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Diagnostics d ON c.ID_probleme = d.ID_probleme LEFT JOIN Observations o ON c.ID_probleme = o.ID_probleme WHERE d.Libelle ILIKE ANY(ARRAY[(use intermediate sql to explore the data have in the databases)]) OR o.Observation ILIKE (use intermediate sql to explore the data have in the databases) ORDER BY c.DateConsultation DESC"
        )

        # Resource utilization queries
        vn.train(
            question="Calculate average consultation time by doctor specialization",
            sql="SELECT m.Specialite, COUNT(c.ID_probleme) as total_consultations, COUNT(DISTINCT c.ID_patient) as unique_patients, ROUND(COUNT(c.ID_probleme)::DECIMAL / COUNT(DISTINCT m.ID_medecin), 2) as avg_consultations_per_doctor FROM Medecins m JOIN Consult_prob c ON m.ID_medecin = c.ID_medecin GROUP BY m.Specialite ORDER BY avg_consultations_per_doctor DESC"
        )

        vn.train(
            question="Show equipment utilization based on examination types",
            sql="SELECT e.TypeExamen, COUNT(*) as usage_count, COUNT(DISTINCT e.ID_probleme) as unique_cases, DATE_TRUNC('month', e.DateExamen) as month FROM Examens_comp e WHERE e.DateExamen >= CURRENT_DATE - INTERVAL '6 months' GROUP BY e.TypeExamen, DATE_TRUNC('month', e.DateExamen) ORDER BY month DESC, usage_count DESC"
        )

        # Add more question-SQL pairs for specialized medical queries

        # Patient demographic queries
        vn.train(
            question="Show me the distribution of patients by gender",
            sql="SELECT Sexe, COUNT(*) as count FROM Patients GROUP BY Sexe ORDER BY count DESC"
        )

        vn.train(
            question="What is the age range of our patients?",
            sql="SELECT MIN(EXTRACT(YEAR FROM AGE(CURRENT_DATE, DateNaissance))) as youngest, MAX(EXTRACT(YEAR FROM AGE(CURRENT_DATE, DateNaissance))) as oldest FROM Patients WHERE DateNaissance IS NOT NULL"
        )

        vn.train(
            question="How many patients do we have in each age decade?",
            sql="SELECT FLOOR(EXTRACT(YEAR FROM AGE(CURRENT_DATE, DateNaissance))/10)*10 as decade, COUNT(*) FROM Patients WHERE DateNaissance IS NOT NULL GROUP BY decade ORDER BY decade"
        )

        # Doctor performance metrics
        vn.train(
            question="Which doctor sees the most patients per day on average?",
            sql="SELECT m.Nom, m.Prenom, m.Specialite, COUNT(c.ID_probleme) / COUNT(DISTINCT DATE(c.DateConsultation)) as avg_patients_per_day FROM Medecins m JOIN Consult_prob c ON m.ID_medecin = c.ID_medecin GROUP BY m.ID_medecin, m.Nom, m.Prenom, m.Specialite HAVING COUNT(DISTINCT DATE(c.DateConsultation)) > 5 ORDER BY avg_patients_per_day DESC"
        )

        vn.train(
            question="Which doctors have the highest rate of repeat consultations?",
            sql="SELECT m.Nom, m.Prenom, m.Specialite, COUNT(c.ID_probleme) as total_consultations, COUNT(DISTINCT c.ID_patient) as unique_patients, ROUND(COUNT(c.ID_probleme)::DECIMAL / COUNT(DISTINCT c.ID_patient), 2) as consultations_per_patient FROM Medecins m JOIN Consult_prob c ON m.ID_medecin = c.ID_medecin GROUP BY m.ID_medecin, m.Nom, m.Prenom, m.Specialite HAVING COUNT(DISTINCT c.ID_patient) >= 10 ORDER BY consultations_per_patient DESC"
        )

        # Treatment effectiveness queries
        vn.train(
            question="Which medications are most effective for treating hypertension?",
            sql="SELECT m.Nom as medication, COUNT(DISTINCT c.ID_probleme) as cases_treated, AVG(b_after.TensionSystolique - b_before.TensionSystolique) as avg_systolic_reduction FROM Medicaments m JOIN Prescriptions pr ON m.ID_medicament = pr.ID_medicament JOIN Consult_prob c ON pr.ID_probleme = c.ID_probleme JOIN Diagnostics d ON c.ID_probleme = d.ID_probleme JOIN Biometrie b_before ON c.ID_patient = b_before.ID_patient AND b_before.ID_probleme = c.ID_probleme LEFT JOIN Biometrie b_after ON c.ID_patient = b_after.ID_patient AND b_after.DateMesure > pr.DatePrescription AND b_after.DateMesure = (SELECT MIN(DateMesure) FROM Biometrie WHERE ID_patient = c.ID_patient AND DateMesure > pr.DatePrescription) WHERE d.Libelle ILIKE '%hypertension%' AND b_before.TensionSystolique > 140 GROUP BY m.ID_medicament, m.Nom HAVING COUNT(DISTINCT c.ID_probleme) >= 5 ORDER BY avg_systolic_reduction"
        )

        vn.train(
            question="Which treatment approach has the best outcomes for diabetes?",
            sql="WITH diabetic_patients AS (SELECT DISTINCT c.ID_patient FROM Consult_prob c JOIN Diagnostics d ON c.ID_probleme = d.ID_probleme WHERE d.Libelle ILIKE (use intermediate sql to explore the data have in the databases) OR d.Libelle ILIKE (use intermediate sql to explore the data have in the databases)) SELECT m.Nom as medication, COUNT(DISTINCT pr.ID_probleme) as prescriptions, COUNT(DISTINCT c_followup.ID_probleme) as followups, ROUND((1 - (COUNT(DISTINCT c_followup.ID_probleme)::DECIMAL / COUNT(DISTINCT pr.ID_probleme))), 2) as improvement_rate FROM diabetic_patients dp JOIN Consult_prob c ON dp.ID_patient = c.ID_patient JOIN Prescriptions pr ON c.ID_probleme = pr.ID_probleme JOIN Medicaments m ON pr.ID_medicament = m.ID_medicament LEFT JOIN Consult_prob c_followup ON dp.ID_patient = c_followup.ID_patient AND c_followup.DateConsultation BETWEEN pr.DatePrescription + INTERVAL '7 days' AND pr.DatePrescription + INTERVAL '90 days' AND c_followup.ID_probleme != c.ID_probleme JOIN Diagnostics d_followup ON c_followup.ID_probleme = d_followup.ID_probleme AND (d_followup.Libelle ILIKE (use intermediate sql to explore the data have in the databases) OR d_followup.Libelle ILIKE (use intermediate sql to explore the data have in the databases)) GROUP BY m.ID_medicament, m.Nom HAVING COUNT(DISTINCT pr.ID_probleme) >= 5 ORDER BY improvement_rate DESC"
        )

        # Patient visit patterns
        vn.train(
            question="What day of the week has the most patient visits?",
            sql="SELECT EXTRACT(DOW FROM DateConsultation) as day_of_week, TO_CHAR(DateConsultation, 'Day') as day_name, COUNT(*) as visit_count FROM Consult_prob GROUP BY EXTRACT(DOW FROM DateConsultation), TO_CHAR(DateConsultation, 'Day') ORDER BY visit_count DESC"
        )

        vn.train(
            question="What time of day do we see the most patients?",
            sql="SELECT EXTRACT(HOUR FROM DateConsultation) as hour_of_day, COUNT(*) as visit_count FROM Consult_prob GROUP BY EXTRACT(HOUR FROM DateConsultation) ORDER BY hour_of_day"
        )

        # Comorbidity analysis
        vn.train(
            question="What conditions commonly occur together?",
            sql="WITH patient_conditions AS (SELECT c.ID_patient, d.Libelle FROM Consult_prob c JOIN Diagnostics d ON c.ID_probleme = d.ID_probleme) SELECT pc1.Libelle as condition1, pc2.Libelle as condition2, COUNT(*) as co_occurrence FROM patient_conditions pc1 JOIN patient_conditions pc2 ON pc1.ID_patient = pc2.ID_patient AND pc1.Libelle < pc2.Libelle GROUP BY pc1.Libelle, pc2.Libelle HAVING COUNT(*) > 3 ORDER BY co_occurrence DESC LIMIT 20"
        )

        vn.train(
            question="Which symptoms frequently appear together?",
            sql="WITH consultation_symptoms AS (SELECT c.ID_probleme, s.Libelle FROM Consult_prob c JOIN Symptomes s ON c.ID_probleme = s.ID_probleme) SELECT cs1.Libelle as symptom1, cs2.Libelle as symptom2, COUNT(*) as co_occurrence FROM consultation_symptoms cs1 JOIN consultation_symptoms cs2 ON cs1.ID_probleme = cs2.ID_probleme AND cs1.Libelle < cs2.Libelle GROUP BY cs1.Libelle, cs2.Libelle HAVING COUNT(*) > 2 ORDER BY co_occurrence DESC LIMIT 15"
        )

        # Medication analysis
        vn.train(
            question="What is the average number of medications prescribed per consultation?",
            sql="SELECT AVG(med_count) as avg_medications_per_consultation FROM (SELECT c.ID_probleme, COUNT(DISTINCT pr.ID_medicament) as med_count FROM Consult_prob c LEFT JOIN Prescriptions pr ON c.ID_probleme = pr.ID_probleme GROUP BY c.ID_probleme) as prescription_counts"
        )

        vn.train(
            question="Which medications are commonly prescribed together?",
            sql="WITH consultation_meds AS (SELECT pr.ID_probleme, m.Nom FROM Prescriptions pr JOIN Medicaments m ON pr.ID_medicament = m.ID_medicament) SELECT cm1.Nom as med1, cm2.Nom as med2, COUNT(*) as co_prescribed FROM consultation_meds cm1 JOIN consultation_meds cm2 ON cm1.ID_probleme = cm2.ID_probleme AND cm1.Nom < cm2.Nom GROUP BY cm1.Nom, cm2.Nom HAVING COUNT(*) > 2 ORDER BY co_prescribed DESC LIMIT 15"
        )

        # Patient follow-up analysis
        vn.train(
            question="What percentage of patients return for follow-up within 30 days?",
            sql="SELECT COUNT(DISTINCT followup.ID_patient)::DECIMAL / COUNT(DISTINCT c.ID_patient) * 100 as followup_percentage FROM Consult_prob c LEFT JOIN LATERAL (SELECT ID_patient FROM Consult_prob WHERE ID_patient = c.ID_patient AND DateConsultation > c.DateConsultation AND DateConsultation <= c.DateConsultation + INTERVAL '30 days' LIMIT 1) followup ON TRUE"
        )

        vn.train(
            question="Which diagnoses require the most follow-up visits?",
            sql="WITH initial_visits AS (SELECT c.ID_patient, c.ID_probleme, c.DateConsultation, d.Libelle as diagnosis FROM Consult_prob c JOIN Diagnostics d ON c.ID_probleme = d.ID_probleme), followups AS (SELECT iv.ID_patient, iv.diagnosis, COUNT(*) as followup_count FROM initial_visits iv JOIN Consult_prob c ON iv.ID_patient = c.ID_patient AND c.DateConsultation > iv.DateConsultation AND c.DateConsultation <= iv.DateConsultation + INTERVAL '90 days' GROUP BY iv.ID_patient, iv.diagnosis) SELECT diagnosis, AVG(followup_count) as avg_followups, COUNT(*) as patient_count FROM followups GROUP BY diagnosis HAVING COUNT(*) >= 5 ORDER BY avg_followups DESC"
        )

        # Specialized care queries
        vn.train(
            question="What specialties are most in demand based on consultation volume?",
            sql="SELECT m.Specialite, COUNT(*) as consultation_count FROM Consult_prob c JOIN Medecins m ON c.ID_medecin = m.ID_medecin GROUP BY m.Specialite ORDER BY consultation_count DESC"
        )

        vn.train(
            question="Which patients require interdisciplinary care?",
            sql="SELECT p.ID_patient, p.Nom, p.Prenom, COUNT(DISTINCT m.Specialite) as specialty_count, STRING_AGG(DISTINCT m.Specialite, ', ') as specialties FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Medecins m ON c.ID_medecin = m.ID_medecin GROUP BY p.ID_patient, p.Nom, p.Prenom HAVING COUNT(DISTINCT m.Specialite) > 1 ORDER BY specialty_count DESC"
        )

        # Additional advanced analytical queries (batch 2)

        # Consultation patterns and patient activity
        vn.train(
            question="What is the average time between consultations for each patient?",
            sql="SELECT p.Nom, p.Prenom, ROUND(AVG(EXTRACT(EPOCH FROM (c.DateConsultation - LAG(c.DateConsultation) OVER (PARTITION BY c.ID_patient ORDER BY c.DateConsultation))) / 86400), 2) AS avg_days_between FROM Consult_prob c JOIN Patients p ON c.ID_patient = p.ID_patient GROUP BY p.ID_patient, p.Nom, p.Prenom HAVING COUNT(c.ID_probleme) > 1 ORDER BY avg_days_between"
        )

        vn.train(
            question="Which patients had the most consultations in the last year?",
            sql="SELECT p.Nom, p.Prenom, COUNT(*) as consultation_count FROM Consult_prob c JOIN Patients p ON c.ID_patient = p.ID_patient WHERE c.DateConsultation >= CURRENT_DATE - INTERVAL '1 year' GROUP BY p.ID_patient, p.Nom, p.Prenom ORDER BY consultation_count DESC LIMIT 10"
        )

        vn.train(
            question="Show monthly consultation trends for the past year",
            sql="SELECT DATE_TRUNC('month', DateConsultation) as month, COUNT(*) as consultation_count FROM Consult_prob WHERE DateConsultation >= CURRENT_DATE - INTERVAL '1 year' GROUP BY month ORDER BY month"
        )

        vn.train(
            question="How many consultations did each age group have in the past year?",
            sql="SELECT CASE WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) < 18 THEN 'Under 18' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) BETWEEN 18 AND 30 THEN '18-30' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) BETWEEN 31 AND 50 THEN '31-50' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) BETWEEN 51 AND 70 THEN '51-70' ELSE 'Over 70' END as age_group, COUNT(*) as consultation_count FROM Consult_prob c JOIN Patients p ON c.ID_patient = p.ID_patient WHERE p.DateNaissance IS NOT NULL AND c.DateConsultation >= CURRENT_DATE - INTERVAL '1 year' GROUP BY age_group ORDER BY consultation_count DESC"
        )

        # Diagnosis-focused analytics
        vn.train(
            question="What are the most common diagnoses among senior patients?",
            sql="SELECT d.Libelle, COUNT(*) as frequency FROM Patients p JOIN Consult_prob c ON p.ID_patient = c.ID_patient JOIN Diagnostics d ON c.ID_probleme = d.ID_probleme WHERE EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) > 60 GROUP BY d.Libelle ORDER BY frequency DESC LIMIT 10"
        )

        # Prescription and medication trends
        vn.train(
            question="Which medications were prescribed most in the last month?",
            sql="SELECT m.Nom, COUNT(*) as prescription_count FROM Medicaments m JOIN Prescriptions pr ON m.ID_medicament = pr.ID_medicament WHERE pr.DatePrescription >= CURRENT_DATE - INTERVAL '1 month' GROUP BY m.ID_medicament, m.Nom ORDER BY prescription_count DESC LIMIT 10"
        )

        vn.train(
            question="What is the average number of prescriptions per patient?",
            sql="SELECT AVG(prescription_count) as avg_prescriptions_per_patient FROM (SELECT p.ID_patient, COUNT(pr.ID_prescription) as prescription_count FROM Patients p LEFT JOIN Consult_prob c ON p.ID_patient = c.ID_patient LEFT JOIN Prescriptions pr ON c.ID_probleme = pr.ID_probleme GROUP BY p.ID_patient) sub"
        )

        vn.train(
            question="Which doctors prescribe the most medications?",
            sql="SELECT m.Nom, m.Prenom, COUNT(*) as prescription_count FROM Medecins m JOIN Consult_prob c ON m.ID_medecin = c.ID_medecin JOIN Prescriptions pr ON c.ID_probleme = pr.ID_probleme GROUP BY m.ID_medecin, m.Nom, m.Prenom ORDER BY prescription_count DESC LIMIT 10"
        )

        # Vital signs monitoring
        vn.train(
            question="Which patients have low oxygen saturation levels?",
            sql="SELECT p.Nom, p.Prenom, b.SpO2, b.DateMesure FROM (SELECT DISTINCT ON (ID_patient) ID_patient, SpO2, DateMesure FROM Biometrie ORDER BY ID_patient, DateMesure DESC) b JOIN Patients p ON p.ID_patient = b.ID_patient WHERE b.SpO2 < 95 ORDER BY b.SpO2"
        )

        vn.train(
            question="Which patients consistently have high blood pressure?",
            sql="SELECT p.Nom, p.Prenom, AVG(b.TensionSystolique) as avg_systolic, AVG(b.TensionDiastolique) as avg_diastolic FROM Patients p JOIN Biometrie b ON p.ID_patient = b.ID_patient WHERE b.DateMesure >= CURRENT_DATE - INTERVAL '6 months' GROUP BY p.ID_patient, p.Nom, p.Prenom HAVING AVG(b.TensionSystolique) > 140 OR AVG(b.TensionDiastolique) > 90 ORDER BY avg_systolic DESC"
        )

        vn.train(
            question="What is the average blood pressure for each age group?",
            sql="SELECT CASE WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) < 18 THEN 'Under 18' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) BETWEEN 18 AND 30 THEN '18-30' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) BETWEEN 31 AND 50 THEN '31-50' WHEN EXTRACT(YEAR FROM AGE(CURRENT_DATE, p.DateNaissance)) BETWEEN 51 AND 70 THEN '51-70' ELSE 'Over 70' END as age_group, AVG(b.TensionSystolique) as avg_systolic, AVG(b.TensionDiastolique) as avg_diastolic FROM Patients p JOIN Biometrie b ON p.ID_patient = b.ID_patient WHERE p.DateNaissance IS NOT NULL GROUP BY age_group ORDER BY age_group"
        )

        # Procedure and symptom analytics
        vn.train(
            question="What is the average number of procedures per consultation?",
            sql="SELECT AVG(proc_count) as avg_procedures_per_consultation FROM (SELECT c.ID_probleme, COUNT(ps.ID_procedure) as proc_count FROM Consult_prob c LEFT JOIN Procedures_soins ps ON c.ID_probleme = ps.ID_probleme GROUP BY c.ID_probleme) sub"
        )

        vn.train(
            question="What is the average number of symptoms recorded per consultation?",
            sql="SELECT AVG(symptom_count) as avg_symptoms_per_consultation FROM (SELECT c.ID_probleme, COUNT(s.ID_symptome) as symptom_count FROM Consult_prob c LEFT JOIN Symptomes s ON c.ID_probleme = s.ID_probleme GROUP BY c.ID_probleme) sub"
        )

        vn.train(
            question="Which diagnoses require the widest variety of medications?",
            sql="SELECT d.Libelle as diagnosis, COUNT(DISTINCT m.Nom) as unique_medications FROM Diagnostics d JOIN Consult_prob c ON d.ID_probleme = c.ID_probleme JOIN Prescriptions pr ON c.ID_probleme = pr.ID_probleme JOIN Medicaments m ON pr.ID_medicament = m.ID_medicament GROUP BY d.Libelle ORDER BY unique_medications DESC LIMIT 20"
        )

        # Examination statistics
        vn.train(
            question="How many examinations of each type were performed in the last year?",
            sql="SELECT TypeExamen, COUNT(*) as exam_count FROM Examens_comp WHERE DateExamen >= CURRENT_DATE - INTERVAL '1 year' GROUP BY TypeExamen ORDER BY exam_count DESC"
        )

        print("Advanced question-SQL training pairs completed successfully.")
        print("Question-SQL training pairs completed successfully.")
        print("Training completed successfully.")
    except Exception as e:
        print(f"Error during training: {e}")

    return vn
