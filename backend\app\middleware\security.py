import re
from fastapi import Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from app.auth.security import decode_token
from starlette import status

class JWTAuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Skip authentication for certain paths
        skip_paths = [
            '/docs',
            '/openapi.json',
            '/auth/login',
            '/auth/token',
            '/auth/refresh',
            '/auth/logout',
            '/api/v1/auth/logout'
        ]
        # Also skip versioned auth paths
        versioned_auth_paths = [
            re.compile(r'^/api/v\d+/auth/login$'),
            re.compile(r'^/api/v\d+/auth/token$'),
            re.compile(r'^/api/v\d+/auth/refresh$')
        ]
        
        # Check if current path is in skip list
        if request.url.path in skip_paths:
            return await call_next(request)
            
        # Check if current path matches any versioned auth pattern
        for pattern in versioned_auth_paths:
            if pattern.match(request.url.path):
                return await call_next(request)

        # Get token from Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid authentication token"}
            )

        token = auth_header.split(' ')[1]
        payload = decode_token(token)
        if not payload:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid or expired token"}
            )

        # Attach user payload to request state
        request.state.user = payload

        return await call_next(request)