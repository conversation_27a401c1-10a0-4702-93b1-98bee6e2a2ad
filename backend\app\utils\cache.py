import time
import uuid
import os
import json
import shutil
import traceback
from pathlib import Path
from collections import OrderedDict
from typing import Any, List, Dict, Optional

class MemoryCache:
    """Optimized MemoryCache with expiration, size limit, and file-based persistence for chat history"""
    
    def __init__(self, max_size: int = 100, expiration_seconds: int = 3600, persistent_dir: str = "cache_data"):
        self.cache = OrderedDict()  # Use OrderedDict for LRU functionality
        self.max_size = max_size
        self.expiration_seconds = expiration_seconds
        self.timestamps = {}  # Track when items were added
        
        # File persistence setup
        self.persistent_dir = persistent_dir
        self.chat_history_dir = os.path.join(self.persistent_dir, "chat_history")
        self.ensure_directories_exist()
        self.load_from_disk()

    def ensure_directories_exist(self):
        """Ensure necessary directories for persistence exist"""
        os.makedirs(self.persistent_dir, exist_ok=True)
        os.makedirs(self.chat_history_dir, exist_ok=True)
    
    def load_from_disk(self):
        """Load previously saved chat histories from disk"""
        try:
            # Load any existing chat histories
            for file_path in Path(self.chat_history_dir).glob("*.json"):
                try:
                    file_path_str = str(file_path)
                    # Check if the file is empty
                    if os.path.getsize(file_path_str) == 0:
                        print(f"Skipping empty file: {file_path_str}")
                        continue
                        
                    with open(file_path_str, 'r', encoding='utf-8') as f:
                        try:
                            data = json.load(f)
                            id = file_path.stem  # Get ID from filename
                            
                            # Restore to in-memory cache
                            self.cache[id] = data
                            self.timestamps[id] = time.time()
                            print(f"Successfully loaded chat history from {file_path_str}")
                        except json.JSONDecodeError as je:
                            print(f"JSON decode error in {file_path_str}: {str(je)}")
                            # Try to backup and recover the file
                            self._backup_corrupted_file(file_path_str)
                except Exception as e:
                    print(f"Error loading chat history {file_path}: {str(e)}")
                    traceback.print_exc()
        except Exception as e:
            print(f"Error during disk cache loading: {str(e)}")
            traceback.print_exc()
            
    def _backup_corrupted_file(self, file_path):
        """Backup a corrupted chat history file and create a new empty one"""
        try:
            backup_path = f"{file_path}.corrupted.{int(time.time())}"
            print(f"Creating backup of corrupted file: {backup_path}")
            shutil.copy2(file_path, backup_path)
            
            # Try to read the file contents as text to perform basic recovery
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Create a basic structure with empty chat history
                id = Path(file_path).stem
                self.cache[id] = {'chat_history': []}
                self.timestamps[id] = time.time()
                
                print(f"Created empty placeholder for corrupted file: {id}")
            except Exception as e:
                print(f"Failed to create placeholder: {str(e)}")
        except Exception as e:
            print(f"Failed to backup corrupted file: {str(e)}")

    def generate_id(self, question: str = None) -> str:
        return str(uuid.uuid4())

    def save_chat_history_to_disk(self, id: str) -> None:
        """Save a specific chat history to disk with improved error handling"""
        if id not in self.cache:
            return
            
        if 'chat_history' not in self.cache[id]:
            return
            
        try:
            # Create a sanitized copy of the chat history data
            # This helps prevent JSON serialization issues
            data_to_save = self._sanitize_for_json(self.cache[id])
            
            file_path = os.path.join(self.chat_history_dir, f"{id}.json")
            # First write to a temporary file then rename for atomic write
            temp_file = f"{file_path}.tmp"
            
            with open(str(temp_file), 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, default=self._json_default)
                
            # Ensure the data was written correctly
            if os.path.exists(str(temp_file)):
                # Validate the JSON file
                try:
                    with open(str(temp_file), 'r', encoding='utf-8') as f:
                        json.load(f)
                    # If we get here, the JSON is valid, so rename the temp file
                    if os.path.exists(str(file_path)):
                        os.remove(str(file_path))
                    os.rename(str(temp_file), str(file_path))
                except json.JSONDecodeError:
                    print(f"Failed JSON validation for {id}, temp file is corrupted")
            else:
                print(f"Failed to write temporary file for {id}")
                
        except Exception as e:
            print(f"Error saving chat history to disk: {str(e)}")
            traceback.print_exc()
            
    def _json_default(self, obj):
        """Handle non-serializable objects during JSON serialization"""
        try:
            return str(obj)
        except:
            return None
            
    def _sanitize_for_json(self, data):
        """Create a sanitized copy of data that can be safely serialized to JSON"""
        if isinstance(data, dict):
            result = {}
            for k, v in data.items():
                if k == 'chat_history' and isinstance(v, list):
                    # Special handling for chat history
                    result[k] = [self._sanitize_chat_message(msg) for msg in v]
                else:
                    result[k] = self._sanitize_for_json(v)
            return result
        elif isinstance(data, list):
            return [self._sanitize_for_json(item) for item in data]
        elif isinstance(data, (int, float, bool, str, type(None))):
            return data
        else:
            # Convert other types to strings
            return str(data)
            
    def _sanitize_chat_message(self, message):
        """Special handling for chat message objects to ensure they're JSON serializable"""
        if not isinstance(message, dict):
            return {'role': 'system', 'content': str(message)}
            
        result = {}
        for k, v in message.items():
            if k == 'content':
                # Ensure content is a string
                if v is None:
                    result[k] = ""
                elif isinstance(v, str):
                    result[k] = v
                else:
                    result[k] = str(v)
            elif k == 'role':
                # Ensure role is one of allowed values
                if v not in ('user', 'assistant', 'system', 'function'):
                    result[k] = 'system'
                else:
                    result[k] = v
            else:
                # Other fields should be serializable
                result[k] = self._sanitize_for_json(v)
        
        # Ensure required fields exist
        if 'role' not in result:
            result['role'] = 'system'
        if 'content' not in result:
            result['content'] = ''
            
        return result

    def set(self, id: str, field: str, value: Any) -> None:
        # Create entry if it doesn't exist
        if id not in self.cache:
            self.cache[id] = {}
            self.timestamps[id] = time.time()
        
        # Update existing entry and timestamp
        self.cache[id][field] = value
        self.timestamps[id] = time.time()
        
        # If we exceeded the max size, remove the oldest item (LRU)
        if len(self.cache) > self.max_size:
            oldest_id = next(iter(self.cache))
            
            # Before deleting, save to disk if it's a chat history
            if 'chat_history' in self.cache[oldest_id]:
                self.save_chat_history_to_disk(oldest_id)
                
            del self.cache[oldest_id]
            del self.timestamps[oldest_id]
            
        # Persist chat history to disk
        if field == 'chat_history':
            self.save_chat_history_to_disk(id)

    def get(self, id: str, field: str) -> Any:
        # Try to load from disk if not in memory and field is chat_history
        if id not in self.cache and field == 'chat_history':
            try:
                file_path = os.path.join(self.chat_history_dir, f"{id}.json")
                file_path_str = str(file_path)
                
                if os.path.exists(file_path_str):
                    # Check if file is empty
                    if os.path.getsize(file_path_str) == 0:
                        print(f"Skipping empty file during get(): {file_path_str}")
                        self.cache[id] = {field: []}
                        self.timestamps[id] = time.time()
                    else:
                        try:
                            with open(file_path_str, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                
                                # Ensure the loaded data is valid and contains the requested field
                                if not isinstance(data, dict):
                                    print(f"Warning: Loaded invalid data structure for {id}, expected dict but got {type(data)}")
                                    data = {field: []}
                                    
                                # Initialize field if not present
                                if field not in data:
                                    data[field] = []
                                    
                                self.cache[id] = data
                                self.timestamps[id] = time.time()
                                print(f"Successfully loaded chat history from disk for ID {id}: {len(data.get(field, []))} messages")
                        except json.JSONDecodeError as je:
                            print(f"JSON decode error loading {id}: {str(je)}")
                            self._backup_corrupted_file(file_path_str)
                            self.cache[id] = {field: []}
                            self.timestamps[id] = time.time()
            except Exception as e:
                print(f"Error loading chat history from disk: {str(e)}")
                traceback.print_exc()
                # Initialize empty entry on error
                if id not in self.cache:
                    self.cache[id] = {field: []}

        # Check if the entry exists and hasn't expired
        if id in self.cache and id in self.timestamps:
            if time.time() - self.timestamps[id] < self.expiration_seconds:
                # Update access time on successful read
                self.timestamps[id] = time.time()
                return self.cache.get(id, {}).get(field)
            else:
                # Save to disk before removing from memory if it's a chat history
                if 'chat_history' in self.cache[id]:
                    self.save_chat_history_to_disk(id)
                
                # Remove expired entry
                del self.cache[id]
                del self.timestamps[id]
        return None

    def get_all(self, field_list: List[str]) -> List[Dict[str, Any]]:
        results = []
        current_time = time.time()
        expired_ids = []
        
        # Load any chat histories from disk that aren't in memory
        if 'chat_history' in field_list:
            try:
                for file_path in Path(self.chat_history_dir).glob("*.json"):
                    id_key = file_path.stem
                    file_path_str = str(file_path)
                    
                    if id_key not in self.cache:
                        try:
                            # Skip empty files
                            if os.path.getsize(file_path_str) == 0:
                                print(f"Skipping empty file in get_all(): {file_path_str}")
                                continue
                                
                            with open(file_path_str, 'r', encoding='utf-8') as f:
                                try:
                                    data = json.load(f)
                                    self.cache[id_key] = data
                                    self.timestamps[id_key] = time.time()
                                except json.JSONDecodeError:
                                    print(f"JSON decode error in get_all() for {file_path_str}")
                                    self._backup_corrupted_file(file_path_str)
                        except Exception as e:
                            print(f"Error loading chat history {file_path}: {str(e)}")
            except Exception as e:
                print(f"Error listing chat history files: {str(e)}")
        
        for id_key, data in self.cache.items():
            # Check if the item has expired
            if current_time - self.timestamps.get(id_key, 0) >= self.expiration_seconds:
                expired_ids.append(id_key)
                continue
                
            if all(field in data for field in field_list):
                item = {'id': id_key}
                for field in field_list:
                    item[field] = data[field]
                results.append(item)
                
        # Clean up expired entries
        for id_key in expired_ids:
            # Save chat history to disk before removing
            if id_key in self.cache and 'chat_history' in self.cache[id_key]:
                self.save_chat_history_to_disk(id_key)
                
            if id_key in self.cache:
                del self.cache[id_key]
            if id_key in self.timestamps:
                del self.timestamps[id_key]
                
        return results

    def get_item(self, id: str) -> Optional[Dict[str, Any]]:
        # Try to load from disk if not in memory
        if id not in self.cache:
            try:
                file_path = os.path.join(self.chat_history_dir, f"{id}.json")
                file_path_str = str(file_path)
                
                if os.path.exists(file_path_str):
                    # Skip empty files
                    if os.path.getsize(file_path_str) == 0:
                        print(f"Skipping empty file in get_item(): {file_path_str}")
                        self.cache[id] = {'chat_history': []}
                        self.timestamps[id] = time.time()
                    else:
                        try:
                            with open(file_path_str, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                self.cache[id] = data
                                self.timestamps[id] = time.time()
                        except json.JSONDecodeError:
                            print(f"JSON decode error in get_item() for {file_path_str}")
                            self._backup_corrupted_file(file_path_str)
                            self.cache[id] = {'chat_history': []}
                            self.timestamps[id] = time.time()
            except Exception as e:
                print(f"Error loading item from disk: {str(e)}")
        
        if id in self.cache and id in self.timestamps:
            if time.time() - self.timestamps[id] < self.expiration_seconds:
                # Update access time
                self.timestamps[id] = time.time()
                return self.cache.get(id)
            else:
                # Save chat history before removing from memory
                if 'chat_history' in self.cache[id]:
                    self.save_chat_history_to_disk(id)
                    
                # Remove expired entry
                del self.cache[id]
                del self.timestamps[id]
                
        return None
        
    def cleanup_old_files(self, max_age_days=30):
        """Clean up chat history files older than the specified number of days"""
        try:
            cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
            for file_path in Path(self.chat_history_dir).glob("*.json"):
                file_path_str = str(file_path)
                try:
                    if os.path.getmtime(file_path_str) < cutoff_time:
                        os.remove(file_path_str)
                except Exception as e:
                    print(f"Error removing old file {file_path_str}: {str(e)}")
        except Exception as e:
            print(f"Error during old file cleanup: {str(e)}")
            traceback.print_exc()
            
    def clean_corrupted_files(self):
        """Scan for and backup corrupted JSON files"""
        try:
            count = 0
            for file_path in Path(self.chat_history_dir).glob("*.json"):
                file_path_str = str(file_path)
                try:
                    # Skip empty files
                    if os.path.getsize(file_path_str) == 0:
                        continue
                        
                    with open(file_path_str, 'r', encoding='utf-8') as f:
                        json.load(f)  # Try to parse JSON
                except json.JSONDecodeError:
                    count += 1
                    print(f"Found corrupted file: {file_path_str}")
                    self._backup_corrupted_file(file_path_str)
            return count
        except Exception as e:
            print(f"Error during corrupted file cleanup: {str(e)}")
            return 0
