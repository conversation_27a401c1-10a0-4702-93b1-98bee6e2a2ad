{"actions.tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "buttons.close": "<PERSON><PERSON><PERSON>", "buttons.closeModal": "<PERSON><PERSON><PERSON> la fenêtre", "buttons.tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "chart.download": "Télécharger", "chart.downloadAsPng": "Télécharger en PNG", "chart.errorRendering": "<PERSON><PERSON>ur lors de l'affichage du graphique", "chart.interactiveInfo": "Graphique interactif - survolez pour plus de détails", "chart.noData": "Aucune donnée de graphique disponible", "chart.resize": "Redimensionner le graphique", "chart.tryRefreshing": "Veuillez essayer de raf<PERSON><PERSON><PERSON><PERSON>", "chat.input.loading": "Chargement", "chat.input.placeholder": "Tapez votre message...", "chat.input.send": "Envoyer le message", "chat.input.sendIcon": "Envoyer", "chat.sessions.defaultTitle_one": "Chat {{count}}", "chat.sessions.defaultTitle_many": "Chat {{count}}", "chat.sessions.defaultTitle_other": "Chat {{count}}", "chatbot.error.chartNotGenerated": "Le graphique n'a pas pu être généré pour le moment.", "chatbot.error.connectionLost": "Erreur : Connexion perdue. Veuillez réessayer.", "chatbot.error.couldNotGenerateChart": "Impossible de générer un graphique pour ces données.", "chatbot.error.couldNotGenerateSummary": "Impossible de générer un résumé.", "chatbot.error.errorGeneratingSummary": "<PERSON><PERSON>ur lors de la génération du résumé.", "chatbot.error.failedToGenerateSummary": "Échec de la génération du résumé.", "chatbot.error.failedToSend": "Erreur : Échec de l'envoi du message. Veuillez réessayer.", "chatbot.error.fetchingChart": "E<PERSON>ur lors de la récupération du graphique.", "chatbot.error.sqlCorrectionBothFailed": "❌ SQL original échoué - La correction automatique a également échoué", "chatbot.error.sqlCorrectionBothFailedDetails": "Détails de l'erreur :\n- Erreur SQL initiale : {{error}}\n- La tentative de correction a également échoué", "chatbot.error.sqlCorrectionInvalid": "❌ SQL original échoué - La correction automatique n'était pas valide", "chatbot.error.sqlCorrectionInvalidDetails": "<PERSON><PERSON><PERSON> de l'erreur :\n- Erreur SQL initiale : {{error}}\n- Correction LLM invalide : {{correction}}", "chatbot.error.sqlExecutionFailed": "Échec de l'exécution SQL : {{error}}", "chatbot.error.sqlExecutionFailedGeneric": "La requête SQL n'a pas pu être exécutée. Veuillez reformuler votre question.", "chatbot.error.sqlExecutionFailedWithReason": "Échec de l'exécution SQL : {{reason}}", "chatbot.error.sqlQueryOrResult": "Une erreur est survenue lors de l'exécution de la requête SQL ou du traitement des résultats.", "chatbot.header.clearChat": "Effacer la <PERSON>", "chatbot.header.clearChatTooltip": "Effacer la discussion en cours", "chatbot.header.history": "Historique", "chatbot.header.title": "Assistant <PERSON> <PERSON><PERSON><PERSON>", "chatbot.header.toggleHistoryTooltip": "Afficher/masquer l'historique", "chatbot.input.placeholder": "Posez votre question sur les données hospitalières...", "chatbot.loadingChat": "Chargement de la discussion...", "chatbot.status.chartNotRecommended": "Graphique non recommandé pour ce type de données.", "chatbot.status.dataRetrieved": "Données récupéré<PERSON> :", "chatbot.status.executingSql": "Exécution de la requête SQL...", "chatbot.status.generatingChart": "Génération du graphique...", "chatbot.status.generatingSummary": "Génération du résumé...", "chatbot.status.noResults": "Aucun résultat trouvé pour cette requête.", "chatbot.status.sqlCorrection": "⚠️ SQL original échoué - Tentative de correction automatique...", "chatbot.status.sqlCorrectionSuccess": "✅ SQL original échoué - Correction automatique réussie et exécutée.", "chatbot.welcome": "Bienvenue sur l'Assistant de Données Hospitalières ! Posez-moi des questions sur les patients, l'hôpital... ou toute autre information de la base de données.", "chatHistorySidebar.clearAllHistory": "Effacer tout l'historique", "chatHistorySidebar.clearSearch": "Effacer la recherche", "chatHistorySidebar.delete": "<PERSON><PERSON><PERSON><PERSON>", "chatHistorySidebar.duplicate": "<PERSON><PERSON><PERSON><PERSON>", "chatHistorySidebar.hoursAgo_one": "il y a {{count}}h", "chatHistorySidebar.hoursAgo_many": "il y a {{count}}h", "chatHistorySidebar.hoursAgo_other": "il y a {{count}}h", "chatHistorySidebar.justNow": "À l'instant", "chatHistorySidebar.newChat": "Nouvelle discussion", "chatHistorySidebar.newConversation": "Nouvelle conversation", "chatHistorySidebar.noChatSessions": "Aucune discussion pour l'instant", "chatHistorySidebar.noConversationsFound": "Aucune conversation trouvée", "chatHistorySidebar.rename": "<PERSON>mmer", "chatHistorySidebar.searchAria": "Rechercher des conversations", "chatHistorySidebar.searchPlaceholder": "Rechercher des conversations...", "chatHistorySidebar.startNewConversation": "<PERSON><PERSON><PERSON><PERSON> une nouvelle conversation", "chatHistorySidebar.title": "Historique des discussions", "chatHistorySidebar.tryDifferentSearch": "Essayez un autre terme de recherche", "chatHistorySidebar.yesterday": "<PERSON>er", "chatMessage.assistantLabel": "Assistant :", "chatMessage.assistantLabelShort": "Assistant :", "chatMessage.assistantThinking": "L'assistant r<PERSON><PERSON><PERSON><PERSON><PERSON>...", "chatMessage.correctedSql": "SQL corrigé {{status}} :", "chatMessage.extractedSql": "SQL extrait :", "chatMessage.failedSqlQuery": "Requête SQL échouée :", "chatMessage.finalSqlQuery": "Requête SQL finale :", "chatMessage.hideFullLlmOutput": "Masquer la sortie complète du LLM", "chatMessage.initialSqlFailed": "SQL initial (échoué) :", "chatMessage.initialSqlFailedBoth": "Le SQL initial a échoué et la tentative de correction automatique a également échoué.", "chatMessage.initialSqlFailedCorrected": "Le SQL initial a échoué mais a été corrigé automatiquement et exécuté avec succès.", "chatMessage.intermediateQueryNumber": "Requête intermédiaire #{{number}}", "chatMessage.processingThoughts": "Traitement en cours", "chatMessage.processingThoughtsGenerating": "Traitement et génération de l'étape suivante...", "chatMessage.resultsNumberRows_one": "Résultats #{{number}} ({{count}} ligne trouvée)", "chatMessage.resultsNumberRows_many": "Résultats #{{number}} ({{count}} lignes trouvées)", "chatMessage.resultsNumberRows_other": "Résultats #{{number}} ({{count}} lignes trouvées)", "chatMessage.scrollToViewMore": "💡 Faites défiler pour voir plus de données", "chatMessage.showFullLlmOutput": "Afficher la sortie complète du LLM", "chatMessage.showingAllRows_one": "Affichage de {{count}} ligne", "chatMessage.showingAllRows_many": "Affichage de {{count}} lignes", "chatMessage.showingAllRows_other": "Affichage de {{count}} lignes", "chatMessage.smartQueryProcessing": "Traitement intelligent des requêtes", "chatMessage.sqlAutoCorrectionApplied": "Correction automatique SQL appliquée", "chatMessage.sqlExecutionFailed": "Échec de l'exécution SQL", "chatMessage.sqlExecutionFailedBoth": "Échec de l'exécution SQL - Les deux tentatives ont échoué", "chatMessage.sqlExecutionFailedMessage": "La requête SQL générée n'a pas pu être exécutée. Veuillez reformuler votre question ou vérifier si les données demandées existent.", "chatMessage.tableDash": "—", "chatMessage.userLabel": "Vous : ", "common.na": "N/A", "common.notAvailable": "N/A", "consultationSearch.errors.doctorIdNotAvailable": "ID du médecin non disponible dans les données de consultation", "consultationSearch.errors.enterSearchTerm": "Veuillez saisir un terme de recherche", "consultationSearch.errors.selectDates": "Veuillez sélectionner une date de début et de fin", "consultationSearch.form.fromDate": "Date de début", "consultationSearch.form.searchButton": "Rechercher des consultations", "consultationSearch.form.searchBy": "Rechercher par", "consultationSearch.form.searching": "Recherche en cours...", "consultationSearch.form.searchTerm": "Terme de recherche", "consultationSearch.form.searchTermPlaceholder": "", "consultationSearch.form.toDate": "Date de fin", "consultationSearch.results.count_one": "({{count}} consultation trouvée)", "consultationSearch.results.count_many": "({{count}} consultations trouvées)", "consultationSearch.results.count_other": "({{count}} consultations trouvées)", "consultationSearch.results.noResults": "Aucune consultation trouvée", "consultationSearch.results.noResultsHint": "Essayez d'ajuster vos critères ou termes de recherche", "consultationSearch.results.title": "Résultats de la recherche", "consultationSearch.subtitle": "Recherchez des consultations par patient, médecin, date ou spécialité", "consultationSearch.table.consultationId": "ID Consultation", "consultationSearch.table.dateTime": "Date & Heure", "consultationSearch.table.diagnosis": "Diagnostic", "consultationSearch.table.doctor": "Médecin", "consultationSearch.table.patient": "Patient", "consultationSearch.table.specialty": "Spécialité", "consultationSearch.title": "Recherche de consultations", "dashboard.buttons.refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.errors.consultationTrends": "", "dashboard.errors.departmentStats": "", "dashboard.errors.patientDemographics": "", "dashboard.errors.recentActivity": "", "dashboard.stats.recentConsultations": "Consultations récentes (30 jours)", "dashboard.stats.totalConsultations": "Total des consultations", "dashboard.stats.totalDoctors": "Total des médecins", "dashboard.stats.totalPatients": "Total des patients", "dashboard.tableHeaders.avgAge": "<PERSON><PERSON> moyen", "dashboard.tableHeaders.consultations": "Consultations", "dashboard.tableHeaders.department": "Département", "dashboard.tableHeaders.doctors": "M<PERSON><PERSON><PERSON><PERSON>", "dashboard.title": "Tableau de bord", "dashboard.widgets.ageDistribution": "Répartition par âge", "dashboard.widgets.consultationsBySpecialty": "Consultations par spécialité", "dashboard.widgets.consultationsCount_one": "{{count}} consultation", "dashboard.widgets.consultationsCount_many": "{{count}} consultations", "dashboard.widgets.consultationsCount_other": "{{count}} consultations", "dashboard.widgets.departmentDetails": "Détails du département", "dashboard.widgets.departmentStatistics": "Statistiques du département", "dashboard.widgets.genderDistribution": "Répartition par sexe", "dashboard.widgets.monthlyConsultationTrends": "Tendances mensuelles des consultations", "dashboard.widgets.monthlyData": "<PERSON><PERSON><PERSON>", "dashboard.widgets.noRecentActivity": "Aucune activité récente trouvée", "dashboard.widgets.patientDemographics": "Démographie des patients", "dashboard.widgets.recentActivity": "Activité récente", "dashboard.widgets.showingRecentActivities_one": "Affichage de {{count}} activité récente", "dashboard.widgets.showingRecentActivities_many": "Affichage de {{count}} activités récentes", "dashboard.widgets.showingRecentActivities_other": "Affichage de {{count}} activités récentes", "dashboard.widgets.specialtyBreakdown": "Répartition par spécialité", "datatable.emptyCell": "-", "datatable.noData": "Aucune donnée à afficher", "datatable.scrollHint": "💡 Faites défiler pour voir plus de données", "datatable.showingRows_one": "Affichage de {{count}} ligne", "datatable.showingRows_many": "Affichage de {{count}} lignes", "datatable.showingRows_other": "Affichage de {{count}} lignes", "doctorSearch.errors.emptyQuery": "Veuillez saisir un terme de recherche", "doctorSearch.errors.searchFailed": "La recherche a échoué. Veuillez réessayer.", "doctorSearch.form.department": "Département", "doctorSearch.form.doctorId": "ID Médecin", "doctorSearch.form.license": "Numéro de licence", "doctorSearch.form.name": "Nom du médecin", "doctorSearch.form.placeholder.department": "<PERSON><PERSON> le département...", "doctorSearch.form.placeholder.doctorId": "Saisir l'ID du médecin...", "doctorSearch.form.placeholder.license": "Saisir le numéro de licence...", "doctorSearch.form.placeholder.name": "Saisir le nom du médecin...", "doctorSearch.form.placeholder.specialty": "Saisir la spécialité...", "doctorSearch.form.searchButton": "Rechercher des médecins", "doctorSearch.form.searchBy": "Rechercher par", "doctorSearch.form.searching": "Recherche en cours...", "doctorSearch.form.searchingDoctors": "Recherche des médecins...", "doctorSearch.form.searchTerm": "Terme de recherche", "doctorSearch.form.specialty": "Spécialité", "doctorSearch.results.count_one": "({{count}} médecin trouvé)", "doctorSearch.results.count_many": "({{count}} médecins trouvés)", "doctorSearch.results.count_other": "({{count}} médecins trouvés)", "doctorSearch.results.noDoctors": "<PERSON><PERSON><PERSON> médecin trouvé", "doctorSearch.results.title": "Résultats de la recherche", "doctorSearch.results.tryAdjusting": "Essayez d'ajuster vos critères ou termes de recherche", "doctorSearch.subtitle": "Recherchez des médecins par nom, spécialité, département ou numéro de licence", "doctorSearch.table.contact": "Contact", "doctorSearch.table.department": "Département", "doctorSearch.table.doctorId": "ID Médecin", "doctorSearch.table.dr": "Dr.", "doctorSearch.table.licenseNumber": "Numéro de licence", "doctorSearch.table.na": "N/A", "doctorSearch.table.name": "Nom", "doctorSearch.table.specialty": "Spécialité", "doctorSearch.table.status": "Statut", "doctorSearch.table.unknown": "Inconnu", "doctorSearch.title": "Recherche de médecins", "errors.authCheckFailed": "Vérification d'authentification échouée :", "errors.fetchPatientDetails": "Échec de la récupération des détails du patient", "errors.loadSettings": "Erreur lors du chargement des paramètres :", "errors.loginError": "Erreur de connexion :", "errors.loginFailed": "Échec de la connexion. Veuillez vérifier vos identifiants.", "errors.loginGeneric": "Une erreur est survenue lors de la connexion. Veuillez réessayer.", "errors.logoutError": "Erreur lors de la déconnexion :", "errors.network": "<PERSON>rreur réseau. Veuillez vérifier votre connexion et réessayer.", "errors.saveSettings": "Erreur lors de l'enregistrement des paramètres :", "errors.tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "footer.copyright": "2077 Système Assistant Hospitalier", "footer.help": "Aide", "footer.privacyPolicy": "Politique de confidentialité", "header.defaultUser": "<PERSON><PERSON>", "header.language.en": "EN", "header.language.fr": "FR", "header.logout": "Déconnexion", "header.title": "SYSTÈME ASSISTANT HOSPITALIER", "header.toggleTheme": "Changer de thème", "loadingSpinner.text": "chargement", "login.demoAccounts.admin": "Admin", "login.demoAccounts.doctor": "Médecin", "login.demoAccounts.nurse": "In<PERSON>rm<PERSON>", "login.demoAccounts.title": "Co<PERSON><PERSON>", "login.footer.copyright": "Système Assistant Hospitalier", "login.form.error": "erreur", "login.form.loggingIn": "Connexion...", "login.form.login": "Connexion", "login.form.passwordLabel": "Mot de passe", "login.form.passwordPlaceholder": "<PERSON><PERSON> le mot de passe", "login.form.title": "Connexion à l'Assistant Hospitalier", "login.form.usernameLabel": "Nom d'utilisateur", "login.form.usernamePlaceholder": "<PERSON><PERSON> le nom d'utilisateur", "login.header.title": "SYSTÈME ASSISTANT HOSPITALIER", "login.header.toggleTheme": "Changer de thème", "modals.consultationDetails.empty.biometrics": "Aucun signe vital enregistré autour de cette consultation.", "modals.consultationDetails.empty.observations": "Aucune observation clinique enregistrée.", "modals.consultationDetails.empty.prescriptions": "Aucune prescription donnée lors de cette consultation.", "modals.consultationDetails.empty.procedures": "Aucune procédure effectuée lors de cette consultation.", "modals.consultationDetails.empty.symptoms": "Aucun symptôme enregistré pour cette consultation.", "modals.consultationDetails.fields.consultationId": "ID Consultation", "modals.consultationDetails.fields.dateTime": "Date & Heure", "modals.consultationDetails.fields.diagnosis": "Diagnostic", "modals.consultationDetails.fields.doctor": "Médecin", "modals.consultationDetails.fields.dosage": "Dosage :", "modals.consultationDetails.fields.form": "Forme :", "modals.consultationDetails.fields.patient": "Patient", "modals.consultationDetails.fields.procedure": "Procédure", "modals.consultationDetails.fields.type": "Type :", "modals.consultationDetails.sections.clinicalObservations": "Observations cliniques", "modals.consultationDetails.sections.consultationInfo": "Informations sur la consultation", "modals.consultationDetails.sections.patientDoctorInfo": "Informations Patient & Médecin", "modals.consultationDetails.sections.vitalSignsAroundConsultation": "Signes vitaux (autour de la consultation)", "modals.consultationDetails.tableHeaders.bloodPressure": "Pression artérielle", "modals.consultationDetails.tableHeaders.date": "Date", "modals.consultationDetails.tableHeaders.observation": "Observation", "modals.consultationDetails.tableHeaders.pulse": "<PERSON><PERSON><PERSON>", "modals.consultationDetails.tableHeaders.respiratoryRate": "Fréq. respiratoire", "modals.consultationDetails.tableHeaders.spo2": "SpO2", "modals.consultationDetails.tableHeaders.temperature": "Température", "modals.consultationDetails.tabs.biometrics": "Signes vitaux (autour de la consultation)", "modals.consultationDetails.tabs.observations": "Observations cliniques", "modals.consultationDetails.tabs.overview": "<PERSON><PERSON><PERSON><PERSON>", "modals.consultationDetails.tabs.prescriptions": "Prescriptions", "modals.consultationDetails.tabs.procedures": "Procédures", "modals.consultationDetails.tabs.symptoms": "<PERSON><PERSON>pt<PERSON><PERSON>", "modals.consultationDetails.title": "<PERSON>é<PERSON> de la consultation", "modals.consultationSearch.consultationId": "ID Consultation", "modals.consultationSearch.consultationRowTooltip": "Cliquez pour voir les détails de la consultation", "modals.consultationSearch.dateRange": "Plage de dates", "modals.consultationSearch.doctorButtonTooltip": "Cliquez pour voir les détails du médecin", "modals.consultationSearch.doctorName": "Nom du médecin", "modals.consultationSearch.fromDate": "Date de début", "modals.consultationSearch.loading": "Recherche des consultations...", "modals.consultationSearch.na": "N/A", "modals.consultationSearch.noResults": "Aucune consultation trouvée", "modals.consultationSearch.noResultsHint": "Essayez d'ajuster vos critères ou termes de recherche", "modals.consultationSearch.patientButtonTooltip": "Cliquez pour voir les détails du patient", "modals.consultationSearch.patientName": "Nom du patient", "modals.consultationSearch.resultsCount_one": "({{count}} consultation trouvée)", "modals.consultationSearch.resultsCount_many": "({{count}} consultations trouvées)", "modals.consultationSearch.resultsCount_other": "({{count}} consultations trouvées)", "modals.consultationSearch.resultsTitle": "Résultats de la recherche", "modals.consultationSearch.searchButton": "Rechercher des consultations", "modals.consultationSearch.searchBy": "Rechercher par", "modals.consultationSearch.searching": "Recherche en cours...", "modals.consultationSearch.searchPlaceholder": "", "modals.consultationSearch.searchTerm": "Terme de recherche", "modals.consultationSearch.searchType": "Type de recherche", "modals.consultationSearch.specialty": "Spécialité", "modals.consultationSearch.table.dateTime": "Date & Heure", "modals.consultationSearch.table.diagnosis": "Diagnostic", "modals.consultationSearch.table.doctor": "Médecin", "modals.consultationSearch.table.id": "ID", "modals.consultationSearch.table.patient": "Patient", "modals.consultationSearch.table.specialty": "Spécialité", "modals.consultationSearch.title": "Recherche de consultations", "modals.consultationSearch.toDate": "Date de fin", "modals.deleteChatSession.cancel": "Annuler", "modals.deleteChatSession.delete": "<PERSON><PERSON><PERSON><PERSON>", "modals.deleteChatSession.title": "Supprimer la discussion ?", "modals.deleteChatSession.warning": "Cette action est irréversible. La discussion et tous ses messages seront définitivement supprimés.", "modals.doctorDetails.buttons.viewConsultation": "Voir la consultation", "modals.doctorDetails.buttons.viewPatient": "Voir le patient", "modals.doctorDetails.consultationId": "Consultation #{{consultationId}}", "modals.doctorDetails.fields.doctorId": "ID Médecin", "modals.doctorDetails.fields.email": "Email", "modals.doctorDetails.fields.fullName": "Nom complet", "modals.doctorDetails.fields.specialty": "Spécialité", "modals.doctorDetails.labels.diagnosis": "Diagnostic :", "modals.doctorDetails.labels.patient": "Patient :", "modals.doctorDetails.noConsultations": "Aucune consultation trouvée.", "modals.doctorDetails.noPatients": "Aucun patient trouvé.", "modals.doctorDetails.noStatistics": "Aucune statistique disponible.", "modals.doctorDetails.sections.activitySummary": "Résumé de l'activité", "modals.doctorDetails.sections.doctorInfo": "Informations sur le médecin", "modals.doctorDetails.sections.patientsTreated": "Patients traités", "modals.doctorDetails.sections.practiceOverview": "Aperçu de la pratique", "modals.doctorDetails.sections.practicePeriod": "Période de pratique", "modals.doctorDetails.sections.practiceStatistics": "Statistiques de la pratique", "modals.doctorDetails.sections.recentConsultations": "Consultations récentes", "modals.doctorDetails.stats.activeDays": "Jours actifs :", "modals.doctorDetails.stats.activeDaysColon": "Jours actifs :", "modals.doctorDetails.stats.firstConsultation": "Première consultation :", "modals.doctorDetails.stats.lastConsultation": "Dernière consultation :", "modals.doctorDetails.stats.patientsTreated": "Patients traités", "modals.doctorDetails.stats.totalConsultations": "Total des consultations :", "modals.doctorDetails.stats.totalConsultationsColon": "Total des consultations :", "modals.doctorDetails.stats.uniquePatients": "Patients uniques :", "modals.doctorDetails.tableHeaders.consultations": "Consultations", "modals.doctorDetails.tableHeaders.lastVisit": "<PERSON><PERSON><PERSON> visite", "modals.doctorDetails.tableHeaders.patientName": "Nom du patient", "modals.doctorDetails.tabs.consultations": "Consultations", "modals.doctorDetails.tabs.overview": "<PERSON><PERSON><PERSON><PERSON>", "modals.doctorDetails.tabs.patients": "Patients", "modals.doctorDetails.tabs.statistics": "Statistiques", "modals.doctorDetails.title": "<PERSON>é<PERSON> du médecin", "modals.doctorSearch.button.search": "Rechercher des médecins", "modals.doctorSearch.button.searching": "Recherche en cours...", "modals.doctorSearch.error.emptyQuery": "Veuillez saisir un terme de recherche", "modals.doctorSearch.error.searchFailed": "La recherche a échoué. Veuillez réessayer.", "modals.doctorSearch.loading": "Recherche des médecins...", "modals.doctorSearch.placeholder.name": "Saisir le nom du médecin...", "modals.doctorSearch.results.count_one": "({{count}} médecin trouvé)", "modals.doctorSearch.results.count_many": "({{count}} médecins trouvés)", "modals.doctorSearch.results.count_other": "({{count}} médecins trouvés)", "modals.doctorSearch.results.noDoctors": "<PERSON><PERSON><PERSON> médecin trouvé", "modals.doctorSearch.results.title": "Résultats de la recherche", "modals.doctorSearch.results.tryAdjusting": "Essayez d'ajuster vos critères ou termes de recherche", "modals.doctorSearch.searchBy": "Rechercher par", "modals.doctorSearch.searchTerm": "Terme de recherche", "modals.doctorSearch.searchType": "Type de recherche", "modals.doctorSearch.tableHeaders.contact": "Contact", "modals.doctorSearch.tableHeaders.department": "Département", "modals.doctorSearch.tableHeaders.doctorId": "ID Médecin", "modals.doctorSearch.tableHeaders.name": "Nom", "modals.doctorSearch.tableHeaders.specialty": "Spécialité", "modals.doctorSearch.tableHeaders.status": "Statut", "modals.doctorSearch.tableRow.dr": "Dr.", "modals.doctorSearch.tableRow.na": "N/A", "modals.doctorSearch.tableRow.viewDoctorDetails": "Cliquez pour voir les détails du médecin", "modals.doctorSearch.title": "Recherche de médecins", "modals.doctorSearch.type.department": "Département", "modals.doctorSearch.type.doctorId": "ID Médecin", "modals.doctorSearch.type.email": "Email", "modals.doctorSearch.type.name": "Nom du médecin", "modals.doctorSearch.type.specialty": "Spécialité", "modals.patientDetails.address": "<PERSON><PERSON><PERSON>", "modals.patientDetails.bloodPressure": "Pression artérielle", "modals.patientDetails.date": "Date", "modals.patientDetails.dateOfBirth": "Date de naissance", "modals.patientDetails.doctor": "Médecin", "modals.patientDetails.dosage": "Dosage", "modals.patientDetails.form": "Forme", "modals.patientDetails.fullName": "Nom complet", "modals.patientDetails.gender": "<PERSON>e", "modals.patientDetails.genderFemale": "<PERSON>mme", "modals.patientDetails.genderMale": "<PERSON><PERSON>", "modals.patientDetails.medicalHistory": "Antécédents médicaux", "modals.patientDetails.noConsultations": "Aucune consultation trouvée.", "modals.patientDetails.noDiagnosis": "Aucun diagnostic", "modals.patientDetails.noMedicalHistory": "Aucun antécédent médical enregistré.", "modals.patientDetails.noPrescriptions": "Aucune prescription trouvée.", "modals.patientDetails.noVitalSigns": "Aucun signe vital enregistré.", "modals.patientDetails.patientId": "ID Patient", "modals.patientDetails.personalInfo": "Informations personnelles", "modals.patientDetails.phone": "Téléphone", "modals.patientDetails.prescribedFor": "Prescrit pour", "modals.patientDetails.pulse": "<PERSON><PERSON><PERSON>", "modals.patientDetails.recentConsultations": "Consultations récentes", "modals.patientDetails.recentMedications": "Médicaments récents", "modals.patientDetails.recentVitalSigns": "Signes vitaux récents", "modals.patientDetails.respiratoryRate": "Fréq. respiratoire", "modals.patientDetails.specialty": "Spécialité", "modals.patientDetails.spo2": "SpO2", "modals.patientDetails.tabs.biometrics": "Signes vitaux", "modals.patientDetails.tabs.consultations": "Consultations", "modals.patientDetails.tabs.history": "Antécédents médicaux", "modals.patientDetails.tabs.overview": "<PERSON><PERSON><PERSON><PERSON>", "modals.patientDetails.tabs.prescriptions": "Médicaments", "modals.patientDetails.temperature": "Température", "modals.patientDetails.title": "<PERSON><PERSON><PERSON> du patient", "modals.patientDetails.unknownDoctor": "Inconnu", "modals.patientSearch.buttons.search": "Rechercher des patients", "modals.patientSearch.buttons.searching": "Recherche en cours...", "modals.patientSearch.error.emptyQuery": "Veuillez saisir un terme de recherche", "modals.patientSearch.error.icon": "<PERSON><PERSON><PERSON>", "modals.patientSearch.error.searchFailed": "La recherche a échoué. Veuillez réessayer.", "modals.patientSearch.loading": "Recherche des patients...", "modals.patientSearch.placeholder.id": "Saisir l'ID du patient...", "modals.patientSearch.placeholder.name": "<PERSON>sir le nom du patient...", "modals.patientSearch.placeholder.phone": "<PERSON><PERSON> le numéro de téléphone...", "modals.patientSearch.results.count_one": "({{count}} patient trouvé)", "modals.patientSearch.results.count_many": "({{count}} patients trouvés)", "modals.patientSearch.results.count_other": "({{count}} patients trouvés)", "modals.patientSearch.results.noPatients": "Aucun patient trouvé", "modals.patientSearch.results.noPatientsHelp": "Essayez d'ajuster vos critères ou termes de recherche", "modals.patientSearch.results.noPatientsIcon": "Aucun patient trouvé", "modals.patientSearch.results.title": "Résultats de la recherche", "modals.patientSearch.searchBy": "Rechercher par", "modals.patientSearch.searchTerm": "Terme de recherche", "modals.patientSearch.searchType.id": "ID Patient", "modals.patientSearch.searchType.name": "Nom du patient", "modals.patientSearch.searchType.phone": "Numéro de téléphone", "modals.patientSearch.table.birthDate": "Date de naissance", "modals.patientSearch.table.contact": "Contact", "modals.patientSearch.table.gender": "<PERSON>e", "modals.patientSearch.table.genderFemale": "<PERSON>mme", "modals.patientSearch.table.genderMale": "<PERSON><PERSON>", "modals.patientSearch.table.na": "N/A", "modals.patientSearch.table.name": "Nom", "modals.patientSearch.table.patientId": "ID Patient", "modals.patientSearch.table.phoneIcon": "Téléphone", "modals.patientSearch.table.rowTooltip": "Cliquez pour voir les détails du patient", "modals.patientSearch.title": "Recherche de patients", "modals.searchModal.badge": "Recherche", "modals.searchModal.tipsText": "Utilisez le menu déroulant pour sélectionner vos critères, puis saisissez votre terme de recherche. Les résultats apparaîtront ci-dessous.", "modals.searchModal.tipsTitle": "Astuces de recherche", "modals.searchModal.title": "titre", "patientSearch.buttons.clear": "<PERSON><PERSON><PERSON><PERSON>", "patientSearch.buttons.search": "<PERSON><PERSON><PERSON>", "patientSearch.buttons.searching": "Recherche en cours...", "patientSearch.errors.emptySearch": "Veuillez saisir un terme de recherche", "patientSearch.errors.searchFailed": "La recherche a échoué. Veuillez réessayer.", "patientSearch.gender.female": "<PERSON>mme", "patientSearch.gender.male": "<PERSON><PERSON>", "patientSearch.gender.unknown": "N/A", "patientSearch.inputPlaceholder": "Saisir {{type}}...", "patientSearch.loading": "Recherche des patients...", "patientSearch.noResults": "Aucun patient trouvé", "patientSearch.noResultsHint": "Essayez d'ajuster vos critères ou terme de recherche.", "patientSearch.results.count_one": "({{count}} patient trouvé)", "patientSearch.results.count_many": "({{count}} patients trouvés)", "patientSearch.results.count_other": "({{count}} patients trouvés)", "patientSearch.results.title": "Résultats de la recherche", "patientSearch.searchBy": "Rechercher par :", "patientSearch.searchType.diagnosis": "Diagnostic", "patientSearch.searchType.email": "Email", "patientSearch.searchType.id": "ID Patient", "patientSearch.searchType.name": "Nom du patient", "patientSearch.searchType.phone": "Numéro de téléphone", "patientSearch.tableHeaders.birthDate": "Date de naissance", "patientSearch.tableHeaders.contact": "Contact", "patientSearch.tableHeaders.fullName": "Nom complet", "patientSearch.tableHeaders.gender": "<PERSON>e", "patientSearch.tableHeaders.patientId": "ID Patient", "patientSearch.title": "Recherche de patients", "settings.about": "À propos", "settings.aboutDesc": "Système Assistant Hospitalier - Plateforme de gestion hospitalière alimentée par l'IA", "settings.appearance": "Apparence", "settings.build": "Version :", "settings.changePassword": "Changer le mot de passe", "settings.confirmNewPassword": "Confirmer le nouveau mot de passe", "settings.currentPassword": "Mot de passe actuel", "settings.currentTheme": "Actuel : mode {{mode}}", "settings.dateFormat": "Format de date", "settings.dateFormatEu": "JJ/MM/AAAA", "settings.dateFormatIso": "AAAA-MM-JJ", "settings.dateFormatUs": "MM/JJ/AAAA", "settings.displayName": "Nom d'affichage", "settings.emailNotifications": "Notifications par email", "settings.emailNotificationsDesc": "Recevoir des notifications par email", "settings.english": "<PERSON><PERSON><PERSON>", "settings.french": "Français", "settings.language": "<PERSON><PERSON>", "settings.lastUpdated": "Dernière mise à jour :", "settings.lastUpdatedDate": "1 janvier 2077", "settings.newPassword": "Nouveau mot de passe", "settings.notifications": "Notifications", "settings.pushNotifications": "Notifications push", "settings.pushNotificationsDesc": "Recevoir des notifications push du navigateur", "settings.pwChangeButton": "Changer le mot de passe", "settings.pwChanging": "Changement en cours...", "settings.pwFailed": "Échec du changement de mot de passe.", "settings.pwNetwork": "<PERSON><PERSON><PERSON> rés<PERSON>. Veuillez réessayer.", "settings.pwNoMatch": "Les nouveaux mots de passe ne correspondent pas.", "settings.pwRequired": "Tous les champs sont obligatoires.", "settings.pwSuccess": "Mot de passe changé avec succès.", "settings.role": "R<PERSON><PERSON>", "settings.saveButton": "Enregistrer les paramètres", "settings.saveFailed": "Échec de l'enregistrement des paramètres. Veuillez réessayer.", "settings.saveSuccess": "Paramètres enregistrés avec succès !", "settings.saving": "Enregistrement...", "settings.switchTheme": "Changer de thème", "settings.switchTo": "Passer en mode {{mode}}", "settings.theme": "Thème", "settings.title": "Paramètres", "settings.toggleOff": "Activer", "settings.toggleOn": "Désactiver", "settings.username": "Nom d'utilisateur", "settings.userProfile": "Profil utilisateur", "settings.version": "Version :", "sidebar.chatbot": "<PERSON><PERSON><PERSON>", "sidebar.closeMenu": "<PERSON><PERSON><PERSON> le menu de navigation", "sidebar.dashboard": "Tableau de bord", "sidebar.navigationMenu": "Menu de navigation", "sidebar.openMenu": "<PERSON><PERSON><PERSON><PERSON>r le menu de navigation", "sidebar.settings": "Paramètres", "user.defaultName": "Utilisa<PERSON>ur", "user.defaultRole": "Utilisa<PERSON>ur", "widget.errorLoadingData": "Erreur lors du chargement des données", "widget.loading": "Chargement de {{title}}..."}