import os
import sys
import logging
from datetime import datetime

# Add backend directory to path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), '../backend'))
from config import Config

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from passlib.context import CryptContext

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/database_manager_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.config = Config()
        self.connection = None
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def connect(self):
        try:
            db_config = self.config.app_database_config
            logger.info(f"Connecting to database: {db_config['host']}:{db_config['port']}/{db_config['dbname']}")
            self.connection = psycopg2.connect(
                host=db_config['host'],
                database=db_config['dbname'],
                user=db_config['user'],
                password=db_config['password'],
                port=db_config['port']
            )
            self.connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            logger.info("Successfully connected to database")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            return False

    def disconnect(self):
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")

    def execute_sql(self, sql, description="SQL execution"):
        try:
            cursor = self.connection.cursor()
            logger.info(f"Executing: {description}")
            cursor.execute(sql)
            cursor.close()
            logger.info(f"Successfully completed: {description}")
            return True
        except Exception as e:
            logger.error(f"Failed {description}: {e}")
            return False

    def purge_all_database(self):
        logger.warning("[WARNING] STARTING DATABASE PURGE - ALL DATA WILL BE LOST!")
        if not self.connect():
            return False
        get_tables_sql = """
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT LIKE 'pg_%' 
        AND tablename != 'information_schema'
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(get_tables_sql)
            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()
            if not tables:
                logger.info("[INFO] No tables found to drop")
            else:
                logger.info(f"[INFO] Found {len(tables)} tables to drop: {', '.join(tables)}")
                for table in tables:
                    quoted_table = f'"{table}"' if table == 'user' else table
                    drop_sql = f"DROP TABLE IF EXISTS {quoted_table} CASCADE"
                    if not self.execute_sql(drop_sql, f"Dropping table {table}"):
                        self.disconnect()
                        return False
                drop_sequences_sql = """
                DO $$ 
                DECLARE 
                    seq_name TEXT;
                BEGIN 
                    FOR seq_name IN 
                        SELECT sequence_name FROM information_schema.sequences 
                        WHERE sequence_schema = 'public'
                    LOOP 
                        EXECUTE 'DROP SEQUENCE IF EXISTS ' || seq_name || ' CASCADE';
                    END LOOP; 
                END $$;
                """
                if not self.execute_sql(drop_sequences_sql, "Dropping all sequences"):
                    self.disconnect()
                    return False
            logger.info("[SUCCESS] Database purge completed successfully")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to purge database: {e}")
            return False
        finally:
            self.disconnect()

    def recreate_database(self):
        logger.info("[INFO] Starting database migration process")
        if not self.connect():
            return False
        try:
            # Create base schema
            logger.info("[INFO] Creating base schema from create_app_db.sql")
            sql_file_path = os.path.join(os.path.dirname(__file__), '../sql/create_app_db.sql')
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            if not self.execute_sql(sql_content, "Creating base schema"):
                return False
            logger.info("[SUCCESS] Base schema created successfully")
            # Add any additional migration steps here if needed
            logger.info("[COMPLETE] Database migration completed successfully!")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to recreate database: {e}")
            return False
        finally:
            self.disconnect()

    def sync_users(self):
        logger.info("[INFO] Starting user sync from hospital database...")
        try:
            # Connect to both hospital and app databases
            hospital_db_config = self.config.hospital_database_config
            app_db_config = self.config.app_database_config
            hospital_conn = psycopg2.connect(
                host=hospital_db_config['host'],
                database=hospital_db_config['dbname'],
                user=hospital_db_config['user'],
                password=hospital_db_config['password'],
                port=hospital_db_config['port']
            )
            app_conn = psycopg2.connect(
                host=app_db_config['host'],
                database=app_db_config['dbname'],
                user=app_db_config['user'],
                password=app_db_config['password'],
                port=app_db_config['port']
            )
            app_conn.autocommit = False

            # Ensure required departments exist
            required_departments = ["Généraliste", "Pneumologue", "Cardiologue", "Dermatologue"]
            with app_conn.cursor() as cursor:
                for dept in required_departments:
                    cursor.execute("SELECT id FROM department WHERE name = %s", (dept,))
                    if not cursor.fetchone():
                        cursor.execute("INSERT INTO department (name) VALUES (%s)", (dept,))
                        logger.info(f"[INFO] Created department '{dept}'")
            app_conn.commit()

            # Ensure required roles exist
            required_roles = ["admin", "medical_staff"]
            with app_conn.cursor() as cursor:
                for role in required_roles:
                    cursor.execute("SELECT id FROM role WHERE name = %s", (role,))
                    if not cursor.fetchone():
                        cursor.execute("INSERT INTO role (name) VALUES (%s)", (role,))
                        logger.info(f"[INFO] Created role '{role}'")
            app_conn.commit()

            # Create admin user if missing
            with app_conn.cursor() as cursor:
                cursor.execute("SELECT id FROM role WHERE name = 'admin'")
                admin_role = cursor.fetchone()
                if admin_role:
                    admin_role_id = admin_role[0]
                    cursor.execute("SELECT id FROM department WHERE name = %s", (required_departments[0],))
                    admin_dept = cursor.fetchone()
                    admin_dept_id = admin_dept[0] if admin_dept else None
                    cursor.execute("SELECT username FROM \"user\" WHERE username = 'admin'")
                    if not cursor.fetchone():
                        password_hash = self.pwd_context.hash('admin123')
                        cursor.execute("""
                            INSERT INTO "user" (
                                username, first_name, last_name, password_hash,
                                department_id, role_id, is_active, created_at, updated_at
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (username) DO NOTHING
                        """, (
                            "admin", "Admin", "User", password_hash,
                            admin_dept_id, admin_role_id, True,
                            datetime.now(), datetime.now()
                        ))
                        logger.info("[INFO] Created admin user")
            app_conn.commit()

            # Get existing usernames
            with app_conn.cursor() as cursor:
                cursor.execute("SELECT username FROM \"user\"")
                existing_usernames = {row[0] for row in cursor.fetchall()}

            # Get doctors from hospital database
            with hospital_conn.cursor() as cursor:
                cursor.execute("""
                    SELECT ID_medecin, Nom, Prenom, Specialite, Email
                    FROM Medecins
                    ORDER BY Nom, Prenom
                """)
                doctors = cursor.fetchall()

            # Create users for doctors
            with app_conn.cursor() as cursor:
                users_created = 0
                cursor.execute("SELECT id FROM role WHERE name = 'medical_staff'")
                role_result = cursor.fetchone()
                if not role_result:
                    logger.warning("[WARNING] Role 'medical_staff' not found. Skipping all doctor user creation.")
                else:
                    role_id = role_result[0]
                    for doctor in doctors:
                        username = self._generate_username(
                            doctor[1],  # Nom
                            doctor[2],  # Prenom
                            existing_usernames
                        )
                        cursor.execute("SELECT id FROM department WHERE name = %s", (doctor[3],))
                        dept_result = cursor.fetchone()
                        if not dept_result:
                            logger.warning(f"[WARNING] Department '{doctor[3]}' not found for doctor '{doctor[1]} {doctor[2]}'. Skipping user creation.")
                            continue
                        department_id = dept_result[0]
                        password_hash = self.pwd_context.hash('doctor123')
                        cursor.execute("""
                            INSERT INTO "user" (
                                username, first_name, last_name, password_hash,
                                department_id, role_id, is_active, created_at, updated_at
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (username) DO NOTHING
                        """, (
                            username,
                            doctor[2] or '',
                            doctor[1] or '',
                            password_hash,
                            department_id,
                            role_id,
                            True,
                            datetime.now(),
                            datetime.now()
                        ))
                        if cursor.rowcount > 0:
                            users_created += 1
                app_conn.commit()
                logger.info(f"[SUCCESS] Created {users_created} doctor users")
            hospital_conn.close()
            app_conn.close()
            logger.info("[COMPLETE] User sync completed successfully")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to sync users: {e}")
            return False

    def _generate_username(self, last_name, first_name, existing_usernames):
        # Simple username generator: lastname + first initial + number, trimmed of all whitespaces
        base_username = f"{last_name.lower()}{first_name[0].lower() if first_name else ''}"
        counter = 1
        username = f"{base_username}{counter}"
        # Remove all whitespaces from username
        username = ''.join(username.split())
        while username in existing_usernames:
            counter += 1
            username = f"{base_username}{counter}"
            username = ''.join(username.split())
        existing_usernames.add(username)
        return username