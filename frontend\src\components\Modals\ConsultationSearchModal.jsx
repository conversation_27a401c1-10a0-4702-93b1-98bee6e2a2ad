import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import LoadingSpinner from '../UI/LoadingSpinner';
import authFetch from '../../utils/authFetch';
import PatientDetailsModal from './PatientDetailsModal';
import ConsultationDetailsModal from './ConsultationDetailsModal';
import DoctorDetailsModal from './DoctorDetailsModal';

const ConsultationSearchModal = () => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('patient_name');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedPatientId, setSelectedPatientId] = useState(null);
  const [showPatientDetails, setShowPatientDetails] = useState(false);
  const [selectedConsultationId, setSelectedConsultationId] = useState(null);
  const [showConsultationDetails, setShowConsultationDetails] = useState(false);
  const [selectedDoctorId, setSelectedDoctorId] = useState(null);
  const [showDoctorDetails, setShowDoctorDetails] = useState(false);

  const handleSearch = async (e) => {
    e.preventDefault();
    
    if (!searchQuery.trim() && searchType !== 'date_range') {
      setError('Please enter a search term');
      return;
    }

    if (searchType === 'date_range' && (!dateFrom || !dateTo)) {
      setError('Please select both start and end dates');
      return;
    }

    setLoading(true);
    setError('');
    setHasSearched(true);

    try {
      const searchData = {
        query: searchQuery.trim(),
        type: searchType,
        date_from: dateFrom,
        date_to: dateTo
      };

      const response = await fetch('/api/v1/search/consultations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(searchData)
      });

      const data = await response.json();

      if (response.ok) {
        setResults(data.results || []);
      } else {
        setError(data.error || 'Search failed. Please try again.');
        setResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setError('Network error. Please check your connection and try again.');
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (timeString) => {
    if (!timeString) return 'N/A';
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handlePatientClick = (consultation, event) => {
    // Stop event propagation to prevent row click
    if (event) {
      event.stopPropagation();
    }

    if (consultation.patient_id) {
      setSelectedPatientId(consultation.patient_id);
      setShowPatientDetails(true);
    }
  };

  const handleClosePatientDetails = () => {
    setShowPatientDetails(false);
    setSelectedPatientId(null);
  };

  const handleConsultationClick = (consultation) => {
    setSelectedConsultationId(consultation.consultation_id);
    setShowConsultationDetails(true);
  };

  const handleCloseConsultationDetails = () => {
    setShowConsultationDetails(false);
    setSelectedConsultationId(null);
  };

  const handleDoctorClick = (consultation, event) => {
    // Stop event propagation to prevent row click
    if (event) {
      event.stopPropagation();
    }

    if (consultation.doctor_id) {
      setSelectedDoctorId(consultation.doctor_id);
      setShowDoctorDetails(true);
    } else {
      console.log('Doctor ID not available in consultation data');
    }
  };

  const handleCloseDoctorDetails = () => {
    setShowDoctorDetails(false);
    setSelectedDoctorId(null);
  };

  // Cross-modal navigation handlers
  const handlePatientClickFromConsultation = (patientId) => {
    setShowConsultationDetails(false);
    setSelectedPatientId(patientId);
    setShowPatientDetails(true);
  };

  const handleDoctorClickFromConsultation = (doctorId) => {
    setShowConsultationDetails(false);
    setSelectedDoctorId(doctorId);
    setShowDoctorDetails(true);
  };

  const handleConsultationClickFromDoctor = (consultationId) => {
    setShowDoctorDetails(false);
    setSelectedConsultationId(consultationId);
    setShowConsultationDetails(true);
  };

  const handlePatientClickFromDoctor = (patientId) => {
    setShowDoctorDetails(false);
    setSelectedPatientId(patientId);
    setShowPatientDetails(true);
  };

  return (
    <div>
      {/* Search Form */}
      <div className="mb-8">
        <form onSubmit={handleSearch} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Search Type */}
            <div>
              <label className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-3">
                {t('modals.consultationSearch.searchBy', { defaultValue: 'Search By' })}
              </label>
              <select
                value={searchType}
                onChange={(e) => setSearchType(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-base"
                aria-label={t('modals.consultationSearch.searchType', { defaultValue: 'Search Type' })}
              >
                <option value="patient_name">{t('modals.consultationSearch.patientName', { defaultValue: 'Patient Name' })}</option>
                <option value="doctor_name">{t('modals.consultationSearch.doctorName', { defaultValue: 'Doctor Name' })}</option>
                <option value="specialty">{t('modals.consultationSearch.specialty', { defaultValue: 'Specialty' })}</option>
                <option value="consultation_id">{t('modals.consultationSearch.consultationId', { defaultValue: 'Consultation ID' })}</option>
                <option value="date_range">{t('modals.consultationSearch.dateRange', { defaultValue: 'Date Range' })}</option>
              </select>
            </div>

            {/* Search Query */}
            {searchType !== 'date_range' && (
              <div>
                <label className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-3">
                  {t('modals.consultationSearch.searchTerm', { defaultValue: 'Search Term' })}
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder={t('modals.consultationSearch.searchPlaceholder', { defaultValue: `Enter ${searchType.replace('_', ' ')}...` })}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-base"
                />
              </div>
            )}

            {/* Date Range */}
            {searchType === 'date_range' && (
              <div>
                <label className="block text-base font-medium text-gray-700 dark:text-gray-300 mb-3">
                  {t('modals.consultationSearch.dateRange', { defaultValue: 'Date Range' })}
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      {t('modals.consultationSearch.fromDate', { defaultValue: 'From Date' })}
                    </label>
                    <input
                      type="date"
                      value={dateFrom}
                      onChange={(e) => setDateFrom(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-base"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      {t('modals.consultationSearch.toDate', { defaultValue: 'To Date' })}
                    </label>
                    <input
                      type="date"
                      value={dateTo}
                      onChange={(e) => setDateTo(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-base"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Search Button */}
          <div className="flex justify-center">
            <button
              type="submit"
              disabled={loading}
              className="px-8 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-semibold rounded-lg transition-all duration-200 flex items-center text-base shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              aria-label={t('modals.consultationSearch.searchButton', { defaultValue: 'Search Consultations' })}
            >
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-3"></i>
                  {t('modals.consultationSearch.searching', { defaultValue: 'Searching...' })}
                </>
              ) : (
                <>
                  <i className="fas fa-search mr-3"></i>
                  {t('modals.consultationSearch.searchButton', { defaultValue: 'Search Consultations' })}
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <i className="fas fa-exclamation-triangle text-red-500 mr-3 text-base"></i>
            <p className="text-base text-red-700 dark:text-red-400">{error}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" text={t('modals.consultationSearch.loading', { defaultValue: 'Searching consultations...' })} />
        </div>
      )}

      {/* Results */}
      {!loading && hasSearched && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {t('modals.consultationSearch.resultsTitle', { defaultValue: 'Search Results' })}
              {results.length > 0 && (
                <span className="ml-3 text-base font-normal text-gray-500 dark:text-gray-400">
                  {t('modals.consultationSearch.resultsCount', { count: results.length, defaultValue: '({{count}} consultation found)', defaultValue_other: '({{count}} consultations found)' })}
                </span>
              )}
            </h3>
          </div>

          {results.length === 0 ? (
            <div className="p-12 text-center">
              <i className="fas fa-search text-4xl text-gray-400 mb-4"></i>
              <p className="text-lg text-gray-500 dark:text-gray-400 mb-2">{t('modals.consultationSearch.noResults', { defaultValue: 'No consultations found' })}</p>
              <p className="text-base text-gray-400 dark:text-gray-500">
                {t('modals.consultationSearch.noResultsHint', { defaultValue: 'Try adjusting your search criteria or search terms' })}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto rounded-lg">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.consultationSearch.table.id', { defaultValue: 'ID' })}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.consultationSearch.table.patient', { defaultValue: 'Patient' })}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.consultationSearch.table.doctor', { defaultValue: 'Doctor' })}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.consultationSearch.table.dateTime', { defaultValue: 'Date & Time' })}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.consultationSearch.table.specialty', { defaultValue: 'Specialty' })}
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('modals.consultationSearch.table.diagnosis', { defaultValue: 'Diagnosis' })}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {results.map((consultation, index) => (
                    <tr
                      key={index}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                      onClick={() => handleConsultationClick(consultation)}
                      title={t('modals.consultationSearch.consultationRowTooltip', { defaultValue: 'Click to view consultation details' })}
                    >
                      <td className="px-3 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                        <span className="text-blue-600 dark:text-blue-400 font-medium">
                          {consultation.consultation_id || t('modals.consultationSearch.na', { defaultValue: 'N/A' })}
                        </span>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {consultation.patient_id ? (
                          <button
                            onClick={(e) => handlePatientClick(consultation, e)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline cursor-pointer transition-colors"
                            title={t('modals.consultationSearch.patientButtonTooltip', { defaultValue: 'Click to view patient details' })}
                          >
                            {consultation.patient_name || t('modals.consultationSearch.na', { defaultValue: 'N/A' })}
                          </button>
                        ) : (
                          consultation.patient_name || t('modals.consultationSearch.na', { defaultValue: 'N/A' })
                        )}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {consultation.doctor_id ? (
                          <button
                            onClick={(e) => handleDoctorClick(consultation, e)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline cursor-pointer transition-colors"
                            title={t('modals.consultationSearch.doctorButtonTooltip', { defaultValue: 'Click to view doctor details' })}
                          >
                            {consultation.doctor_name || t('modals.consultationSearch.na', { defaultValue: 'N/A' })}
                          </button>
                        ) : (
                          consultation.doctor_name || t('modals.consultationSearch.na', { defaultValue: 'N/A' })
                        )}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div>
                          <div>{formatDate(consultation.consultation_date)}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {formatTime(consultation.consultation_time)}
                          </div>
                        </div>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {consultation.specialty || t('modals.consultationSearch.na', { defaultValue: 'N/A' })}
                      </td>
                      <td className="px-3 py-3 text-sm text-gray-900 dark:text-gray-100">
                        <div className="max-w-xs truncate" title={consultation.diagnosis}>
                          {consultation.diagnosis || t('modals.consultationSearch.na', { defaultValue: 'N/A' })}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Patient Details Modal */}
      <PatientDetailsModal
        isOpen={showPatientDetails}
        onClose={handleClosePatientDetails}
        patientId={selectedPatientId}
      />

      {/* Consultation Details Modal */}
      <ConsultationDetailsModal
        isOpen={showConsultationDetails}
        onClose={handleCloseConsultationDetails}
        consultationId={selectedConsultationId}
        onPatientClick={handlePatientClickFromConsultation}
        onDoctorClick={handleDoctorClickFromConsultation}
      />

      {/* Doctor Details Modal */}
      <DoctorDetailsModal
        isOpen={showDoctorDetails}
        onClose={handleCloseDoctorDetails}
        doctorId={selectedDoctorId}
        onPatientClick={handlePatientClickFromDoctor}
        onConsultationClick={handleConsultationClickFromDoctor}
      />
    </div>
  );
};

export default ConsultationSearchModal;
