{"name": "hospital-assistant-frontend", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "plotly.js": "^2.18.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.6.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.13", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.4.21", "tailwindcss": "^3.2.7"}, "proxy": "http://localhost:8000"}