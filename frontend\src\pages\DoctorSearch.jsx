
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import authFetch from '../utils/authFetch';


const DoctorSearch = () => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('name');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (e) => {
    e.preventDefault();
    

    if (!searchQuery.trim()) {
      setError(t('doctorSearch.errors.emptyQuery', { defaultValue: 'Please enter a search term' }));
      return;
    }

    setLoading(true);
    setError('');
    setHasSearched(true);

    try {
      const response = await fetch('/api/v1/search/doctors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: searchQuery.trim(),
          type: searchType
        })
      });

      const data = await response.json();

      if (response.ok) {
        setResults(data.results || []);
      } else {
        setError(data.error || t('doctorSearch.errors.searchFailed', { defaultValue: 'Search failed. Please try again.' }));
        setResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setError(t('errors.network', { defaultValue: 'Network error. Please check your connection and try again.' }));
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          <i className="fas fa-user-md text-purple-600 dark:text-purple-400 mr-3"></i>
          {t('doctorSearch.title', { defaultValue: 'Doctor Search' })}
        </h1>
        <p className="text-base text-gray-600 dark:text-gray-400">
          {t('doctorSearch.subtitle', { defaultValue: 'Search for doctors by name, specialty, department, or license number' })}
        </p>
      </div>

      {/* Search Form */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Search Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('doctorSearch.form.searchBy', { defaultValue: 'Search By' })}
              </label>
              <select
                value={searchType}
                onChange={(e) => setSearchType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="name">{t('doctorSearch.form.name', { defaultValue: 'Doctor Name' })}</option>
                <option value="specialty">{t('doctorSearch.form.specialty', { defaultValue: 'Specialty' })}</option>
                <option value="department">{t('doctorSearch.form.department', { defaultValue: 'Department' })}</option>
                <option value="license">{t('doctorSearch.form.license', { defaultValue: 'License Number' })}</option>
                <option value="doctor_id">{t('doctorSearch.form.doctorId', { defaultValue: 'Doctor ID' })}</option>
              </select>
            </div>

            {/* Search Query */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('doctorSearch.form.searchTerm', { defaultValue: 'Search Term' })}
              </label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={
                  searchType === 'name'
                    ? t('doctorSearch.form.placeholder.name', { defaultValue: 'Enter doctor name...' })
                    : searchType === 'specialty'
                    ? t('doctorSearch.form.placeholder.specialty', { defaultValue: 'Enter specialty...' })
                    : searchType === 'department'
                    ? t('doctorSearch.form.placeholder.department', { defaultValue: 'Enter department...' })
                    : searchType === 'license'
                    ? t('doctorSearch.form.placeholder.license', { defaultValue: 'Enter license number...' })
                    : t('doctorSearch.form.placeholder.doctorId', { defaultValue: 'Enter doctor ID...' })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>

          {/* Search Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-medium rounded-md transition-colors duration-200 flex items-center"
              aria-label={t('doctorSearch.form.searchButton', { defaultValue: 'Search Doctors' })}
            >
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  {t('doctorSearch.form.searching', { defaultValue: 'Searching...' })}
                </>
              ) : (
                <>
                  <i className="fas fa-search mr-2"></i>
                  {t('doctorSearch.form.searchButton', { defaultValue: 'Search Doctors' })}
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <i className="fas fa-exclamation-triangle text-red-500 mr-3"></i>
            <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" text={t('doctorSearch.form.searchingDoctors', { defaultValue: 'Searching doctors...' })} />
        </div>
      )}

      {/* Results */}
      {!loading && hasSearched && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {t('doctorSearch.results.title', { defaultValue: 'Search Results' })}
              {results.length > 0 && (
                <span className="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
                  {t('doctorSearch.results.count', {
                    count: results.length,
                    defaultValue: '({{count}} doctor found)',
                    defaultValue_other: '({{count}} doctors found)'
                  })}
                </span>
              )}
            </h3>
          </div>

          {results.length === 0 ? (
            <div className="p-8 text-center">
              <i className="fas fa-search text-4xl text-gray-400 mb-4"></i>
              <p className="text-lg text-gray-500 dark:text-gray-400 mb-2">
                {t('doctorSearch.results.noDoctors', { defaultValue: 'No doctors found' })}
              </p>
              <p className="text-sm text-gray-400 dark:text-gray-500">
                {t('doctorSearch.results.tryAdjusting', { defaultValue: 'Try adjusting your search criteria or search terms' })}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('doctorSearch.table.doctorId', { defaultValue: 'Doctor ID' })}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('doctorSearch.table.name', { defaultValue: 'Name' })}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('doctorSearch.table.specialty', { defaultValue: 'Specialty' })}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('doctorSearch.table.department', { defaultValue: 'Department' })}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('doctorSearch.table.licenseNumber', { defaultValue: 'License Number' })}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('doctorSearch.table.contact', { defaultValue: 'Contact' })}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {t('doctorSearch.table.status', { defaultValue: 'Status' })}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {results.map((doctor, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                        {doctor.doctor_id || t('doctorSearch.table.na', { defaultValue: 'N/A' })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div>
                          <div className="font-medium">{doctor.first_name} {doctor.last_name}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {t('doctorSearch.table.dr', { defaultValue: 'Dr.' })} {doctor.last_name}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {doctor.specialty || t('doctorSearch.table.na', { defaultValue: 'N/A' })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {doctor.department || t('doctorSearch.table.na', { defaultValue: 'N/A' })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {doctor.license_number || t('doctorSearch.table.na', { defaultValue: 'N/A' })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div>
                          {doctor.phone && (
                            <div className="text-xs">
                              <i className="fas fa-phone mr-1"></i>
                              {doctor.phone}
                            </div>
                          )}
                          {doctor.email && (
                            <div className="text-xs">
                              <i className="fas fa-envelope mr-1"></i>
                              {doctor.email}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          doctor.status === 'active' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : doctor.status === 'inactive'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}>
                          {doctor.status || t('doctorSearch.table.unknown', { defaultValue: 'Unknown' })}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DoctorSearch;
