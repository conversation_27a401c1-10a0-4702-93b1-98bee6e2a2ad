[project]
name = "hospital-chatbot"
version = "0.1.0"
requires-python = ">=3.11"
dependencies = [
    "chromadb>=0.6.3",
    "fastapi[standard]>=0.116.0",
    "ollama>=0.5.1",
    "pandas>=2.3.1",
    "passlib[bcrypt]>=1.7.4",
    "plotly>=6.2.0",
    "psycopg2-binary>=2.9.10",
    "pytest>=8.4.1",
    "python-dotenv>=1.1.1",
    "python-jose[cryptography]>=3.5.0",
    "python-multipart>=0.0.20",
    "sqlalchemy>=2.0.41",
    "vanna[chromadb,ollama]>=0.7.9",
]
