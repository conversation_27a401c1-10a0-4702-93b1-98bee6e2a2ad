import pytest
import sys
import os
from datetime import timedelta

# Add backend directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from fastapi.testclient import TestClient
from main import app
from app.models import get_user_by_username
from app.auth.security import (
    verify_password,
    create_access_token,
    decode_token,
    get_password_hash,
)

client = TestClient(app)

# Test data
TEST_USERNAME = "admin"
TEST_PASSWORD = "admin123"


@pytest.fixture(scope="module")
def test_user():
    # This would ideally be set up in a test database
    # For now, we'll assume the user exists in the test database
    return get_user_by_username(TEST_USERNAME)


def test_token_endpoint_valid_credentials():
    response = client.post(
        "/api/v1/auth/token",
        data={"username": TEST_USERNAME, "password": TEST_PASSWORD},
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

    # Verify token content
    payload = decode_token(data["access_token"])
    assert payload["sub"] == TEST_USERNAME


def test_token_endpoint_invalid_credentials():
    response = client.post(
        "/api/v1/auth/token", data={"username": "invalid", "password": "invalid"}
    )
    assert response.status_code == 401
    assert response.json()["detail"] == "Incorrect username or password"


def test_login_endpoint_valid_credentials():
    response = client.post(
        "/api/v1/auth/login",
        json={"username": TEST_USERNAME, "password": TEST_PASSWORD},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["type"] == "success"
    assert "access_token" in data
    assert data["user"]["username"] == TEST_USERNAME


def test_login_endpoint_invalid_credentials():
    response = client.post(
        "/api/v1/auth/login", json={"username": "invalid", "password": "invalid"}
    )
    assert response.status_code == 401
    assert response.json()["detail"] == "Invalid username or password"


def test_protected_endpoint_valid_token(test_user):
    token = create_access_token({"sub": TEST_USERNAME})
    response = client.get(
        "/api/v1/auth/me", headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    assert response.json()["username"] == TEST_USERNAME


def test_protected_endpoint_invalid_token():
    response = client.get(
        "/api/v1/auth/me", headers={"Authorization": "Bearer invalidtoken"}
    )
    assert response.status_code == 401
    assert response.json()["detail"] == "Invalid or expired token"


def test_password_hashing():
    hashed = get_password_hash(TEST_PASSWORD)
    assert verify_password(TEST_PASSWORD, hashed)
    assert not verify_password("wrongpassword", hashed)


def test_jwt_middleware_valid_token(test_user):
    """Test JWT middleware with valid token"""
    token = create_access_token({"sub": TEST_USERNAME})
    response = client.get(
        "/api/v1/auth/me", headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200


def test_jwt_middleware_invalid_token():
    """Test JWT middleware with invalid token"""
    response = client.get(
        "/api/v1/auth/me", headers={"Authorization": "Bearer invalidtoken"}
    )
    assert response.status_code == 401
    assert response.json()["detail"] == "Invalid or expired token"


def test_jwt_middleware_expired_token():
    """Test JWT middleware with expired token"""
    token = create_access_token(
        data={"sub": TEST_USERNAME},
        expires_delta=timedelta(seconds=-1),  # Expired immediately
    )
    response = client.get(
        "/api/v1/auth/me", headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 401
    assert response.json()["detail"] == "Invalid or expired token"


def test_password_complexity_validation():
    """Test password complexity validation"""
    from app.models.user import User

    # Valid password
    assert User.validate_password_complexity("ValidPass123!") == True

    # Too short
    assert User.validate_password_complexity("Short1!") == False

    # Missing digit
    assert User.validate_password_complexity("NoDigit!") == False

    # Missing uppercase
    assert User.validate_password_complexity("nouppercase123!") == False

    # Missing lowercase
    assert User.validate_password_complexity("NOLOWERCASE123!") == False

    # Missing special character
    assert User.validate_password_complexity("MissingSpecial123") == False


def test_token_refresh_flow():
    """Test full token refresh flow"""
    # First login to get tokens
    login_res = client.post(
        "/api/v1/auth/login",
        json={"username": TEST_USERNAME, "password": TEST_PASSWORD},
    )
    assert login_res.status_code == 200
    access_token = login_res.json()["access_token"]
    refresh_token = login_res.cookies.get("refresh_token")

    # Use refresh token to get new access token
    refresh_res = client.post(
        "/api/v1/auth/refresh", cookies={"refresh_token": refresh_token}
    )
    assert refresh_res.status_code == 200
    new_access_token = refresh_res.json()["access_token"]
    new_refresh_token = refresh_res.cookies.get("refresh_token")

    # Verify new tokens work
    me_res = client.get(
        "/api/v1/auth/me", headers={"Authorization": f"Bearer {new_access_token}"}
    )
    assert me_res.status_code == 200

    # Verify old refresh token is revoked
    revoked_res = client.post(
        "/api/v1/auth/refresh", cookies={"refresh_token": refresh_token}
    )
    assert revoked_res.status_code == 401


def test_expired_refresh_token(test_user):
    """Test using an expired refresh token"""
    from datetime import timedelta

    expired_refresh_token = create_access_token(
        {"sub": test_user.username, "refresh": True},
        expires_delta=timedelta(seconds=-1),
    )
    res = client.post(
        "/api/v1/auth/refresh", cookies={"refresh_token": expired_refresh_token}
    )
    assert res.status_code == 401
    assert res.json()["detail"] in [
        "Invalid refresh token",
        "Refresh token missing",
        "Refresh token revoked",
    ]


def test_automatic_logout_on_expiration(test_user):
    """Test automatic logout when access token is expired"""
    from datetime import timedelta

    expired_token = create_access_token(
        {"sub": test_user.username}, expires_delta=timedelta(seconds=-1)
    )
    res = client.get(
        "/api/v1/auth/me", headers={"Authorization": f"Bearer {expired_token}"}
    )
    assert res.status_code == 401
    assert res.json()["detail"] == "Invalid or expired token"


def test_silent_refresh_one_minute_before_expiration(test_user):
    """Test silent refresh 1 minute before access token expiration"""
    from datetime import timedelta

    # Create a token that expires in 61 seconds
    short_lived_token = create_access_token(
        {"sub": test_user.username}, expires_delta=timedelta(seconds=61)
    )
    # Simulate client logic: refresh 1 minute before expiration
    # Wait 2 seconds to ensure token is within the 1-minute window
    import time

    time.sleep(2)
    res = client.get(
        "/api/v1/auth/me", headers={"Authorization": f"Bearer {short_lived_token}"}
    )
    # Should still be valid
    assert res.status_code == 200

    # Now simulate refresh endpoint (should succeed)
    login_res = client.post(
        "/api/v1/auth/login",
        json={"username": test_user.username, "password": "admin123"},
    )
    refresh_token = login_res.cookies.get("refresh_token")
    refresh_res = client.post(
        "/api/v1/auth/refresh", cookies={"refresh_token": refresh_token}
    )
    assert refresh_res.status_code == 200
    assert "access_token" in refresh_res.json()


def test_token_revocation_after_logout(test_user):
    """Test that refresh token is revoked after logout"""
    # Login to get refresh token
    login_res = client.post(
        "/api/v1/auth/login",
        json={"username": test_user.username, "password": "admin123"},
    )
    refresh_token = login_res.cookies.get("refresh_token")
    # Set the refresh token cookie on the client
    client.cookies.set("refresh_token", refresh_token)
    # Logout
    logout_res = client.post("/api/v1/auth/logout")
    assert logout_res.status_code == 200
    # Try to use the same refresh token
    refresh_res = client.post(
        "/api/v1/auth/refresh", cookies={"refresh_token": refresh_token}
    )
    assert refresh_res.status_code == 401
    assert refresh_res.json()["detail"] in [
        "Refresh token revoked",
        "Invalid refresh token",
        "Refresh token missing",
    ]


def test_audit_log_entry_on_login(monkeypatch):
    """Test that an audit log entry is created on login"""
    audit_log = []

    def mock_audit_log_entry(event, user):
        audit_log.append((event, user))

    # Patch the audit logging function if it exists
    try:
        from app.services import audit_service

        monkeypatch.setattr(audit_service, "log_event", mock_audit_log_entry)
    except ImportError:
        # If audit logging is not implemented, skip this test
        import pytest

        pytest.skip("Audit logging not implemented")

    res = client.post(
        "/api/v1/auth/login",
        json={"username": TEST_USERNAME, "password": TEST_PASSWORD},
    )
    assert res.status_code == 200
    assert any(event == "login" and user == TEST_USERNAME for event, user in audit_log)
