import logging
import traceback
from typing import Optional
from fastapi import APIRouter, HTTPException, Request, Depends
from pydantic import BaseModel
from app.utils.dependencies import get_vanna

# Create router
router = APIRouter(tags=["search"])
logger = logging.getLogger(__name__)

class PatientSearchRequest(BaseModel):
    query: str
    type: str  # 'name', 'id', 'phone', 'diagnosis'

class ConsultationSearchRequest(BaseModel):
    query: str
    type: str  # 'patient_name', 'doctor_name', 'specialty', 'diagnosis', 'consultation_id', 'date_range'
    date_from: Optional[str] = None
    date_to: Optional[str] = None

class DoctorSearchRequest(BaseModel):
    query: str
    type: str  # 'name', 'specialty', 'department', 'license', 'doctor_id'

@router.post("/search/patients")
async def search_patients(
    search_request: PatientSearchRequest,
    request: Request,
    vn = Depends(get_vanna)
):
    """Search for patients based on different criteria"""
    try:
        query = search_request.query.strip()
        search_type = search_request.type
        
        if not query:
            raise HTTPException(
                status_code=400, 
                detail={"error": "Search query cannot be empty"}
            )
        
        # Build SQL query based on search type
        if search_type == "name":
            sql_query = """
                SELECT
                    p.ID_patient as patient_id,
                    CONCAT(p.Prenom, ' ', p.Nom) as name,
                    p.Nom as last_name,
                    p.Prenom as first_name,
                    p.DateNaissance as date_of_birth,
                    p.Sexe as gender,
                    p.Telephone as phone,

                    p.Adresse as address,
                    COALESCE(latest_consult.DateConsultation::text, 'No consultations') as last_visit,
                    COALESCE(latest_consult.LibelleProbleme, 'No diagnosis') as condition
                FROM Patients p
                LEFT JOIN (
                    SELECT DISTINCT ON (cp.ID_patient)
                        cp.ID_patient,
                        cp.DateConsultation,
                        cp.LibelleProbleme
                    FROM Consult_prob cp
                    ORDER BY cp.ID_patient, cp.DateConsultation DESC
                ) latest_consult ON p.ID_patient = latest_consult.ID_patient
                WHERE
                    LOWER(p.Nom) LIKE LOWER(%s) OR
                    LOWER(p.Prenom) LIKE LOWER(%s) OR
                    LOWER(CONCAT(p.Prenom, ' ', p.Nom)) LIKE LOWER(%s)
                ORDER BY p.Nom, p.Prenom
                LIMIT 50
            """
            search_pattern = f"%{query}%"
            params = [search_pattern, search_pattern, search_pattern]
            
        elif search_type == "id":
            sql_query = """
                SELECT
                    p.ID_patient as patient_id,
                    CONCAT(p.Prenom, ' ', p.Nom) as name,
                    p.Nom as last_name,
                    p.Prenom as first_name,
                    p.DateNaissance as date_of_birth,
                    p.Sexe as gender,
                    p.Telephone as phone,

                    p.Adresse as address,
                    COALESCE(latest_consult.DateConsultation::text, 'No consultations') as last_visit,
                    COALESCE(latest_consult.LibelleProbleme, 'No diagnosis') as condition
                FROM Patients p
                LEFT JOIN (
                    SELECT DISTINCT ON (cp.ID_patient)
                        cp.ID_patient,
                        cp.DateConsultation,
                        cp.LibelleProbleme
                    FROM Consult_prob cp
                    ORDER BY cp.ID_patient, cp.DateConsultation DESC
                ) latest_consult ON p.ID_patient = latest_consult.ID_patient
                WHERE CAST(p.ID_patient AS TEXT) = %s OR CAST(p.ID_patient AS TEXT) LIKE %s
                ORDER BY p.ID_patient
                LIMIT 50
            """
            params = [query, f"%{query}%"]
            
        elif search_type == "phone":
            sql_query = """
                SELECT
                    p.ID_patient as patient_id,
                    CONCAT(p.Prenom, ' ', p.Nom) as name,
                    p.Nom as last_name,
                    p.Prenom as first_name,
                    p.DateNaissance as date_of_birth,
                    p.Sexe as gender,
                    p.Telephone as phone,

                    p.Adresse as address,
                    COALESCE(latest_consult.DateConsultation::text, 'No consultations') as last_visit,
                    COALESCE(latest_consult.LibelleProbleme, 'No diagnosis') as condition
                FROM Patients p
                LEFT JOIN (
                    SELECT DISTINCT ON (cp.ID_patient)
                        cp.ID_patient,
                        cp.DateConsultation,
                        cp.LibelleProbleme
                    FROM Consult_prob cp
                    ORDER BY cp.ID_patient, cp.DateConsultation DESC
                ) latest_consult ON p.ID_patient = latest_consult.ID_patient
                WHERE p.Telephone LIKE %s
                ORDER BY p.Nom, p.Prenom
                LIMIT 50
            """
            params = [f"%{query}%"]
            

        elif search_type == "diagnosis":
            sql_query = """
                SELECT DISTINCT
                    p.ID_patient as patient_id,
                    CONCAT(p.Prenom, ' ', p.Nom) as name,
                    p.Nom as last_name,
                    p.Prenom as first_name,
                    p.DateNaissance as date_of_birth,
                    p.Sexe as gender,
                    p.Telephone as phone,

                    p.Adresse as address,
                    cp.DateConsultation::text as last_visit,
                    cp.LibelleProbleme as condition
                FROM Patients p
                INNER JOIN Consult_prob cp ON p.ID_patient = cp.ID_patient
                WHERE LOWER(cp.LibelleProbleme) LIKE LOWER(%s)
                ORDER BY cp.DateConsultation DESC, p.Nom, p.Prenom
                LIMIT 50
            """
            params = [f"%{query}%"]
            
        else:
            raise HTTPException(
                status_code=400,
                detail={"error": f"Invalid search type: {search_type}"}
            )
        
        # Execute the query using Vanna's run_sql method
        # Note: Vanna's run_sql doesn't support parameterized queries directly,
        # so we need to build the SQL string safely
        safe_sql = sql_query
        for param in params:
            safe_sql = safe_sql.replace('%s', f"'{param.replace(chr(39), chr(39)+chr(39))}'", 1)
        
        logger.info(f"Executing patient search query: {safe_sql}")
        
        # Execute query
        df = vn.run_sql(safe_sql)
        
        # Convert DataFrame to list of dictionaries
        if df.empty:
            results = []
        else:
            # Debug: Log the DataFrame columns to see what's actually being returned
            logger.info(f"Patient DataFrame columns: {list(df.columns)}")
            logger.info(f"Patient DataFrame sample: {df.head().to_dict('records') if not df.empty else 'Empty'}")

            results = df.to_dict('records')

            # Format the results for frontend consumption
            formatted_results = []
            for row in results:
                # Debug: Log the row data to see what's actually being returned
                logger.info(f"Patient row data: {dict(row)}")

                formatted_row = {
                    "patient_id": str(row.get('patient_id', '')),  # Fixed: use 'patient_id' not 'id'
                    "name": row.get('name', ''),
                    "first_name": row.get('first_name', ''),
                    "last_name": row.get('last_name', ''),
                    "date_of_birth": str(row.get('date_of_birth', '')) if row.get('date_of_birth') else '',
                    "gender": row.get('gender', ''),
                    "phone": row.get('phone', ''),
                    "email": '',  # Email field not available in database
                    "address": row.get('address', ''),
                    "last_visit": str(row.get('last_visit', '')) if row.get('last_visit') else 'No consultations',
                    "condition": row.get('condition', 'No diagnosis')
                }
                formatted_results.append(formatted_row)

            results = formatted_results
        
        logger.info(f"Patient search completed. Found {len(results)} results for query '{query}' (type: {search_type})")
        
        return {
            "status": "success",
            "results": results,
            "total": len(results),
            "query": query,
            "search_type": search_type
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error in patient search: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail={"error": "Search failed. Please try again."}
        )

@router.post("/search/consultations")
async def search_consultations(
    search_request: ConsultationSearchRequest,
    request: Request,
    vn = Depends(get_vanna)
):
    """Search for consultations based on different criteria"""
    try:
        query = search_request.query.strip() if search_request.query else ""
        search_type = search_request.type
        date_from = search_request.date_from
        date_to = search_request.date_to

        if search_type != 'date_range' and not query:
            raise HTTPException(
                status_code=400,
                detail={"error": "Search query cannot be empty"}
            )

        if search_type == 'date_range' and (not date_from or not date_to):
            raise HTTPException(
                status_code=400,
                detail={"error": "Both start and end dates are required for date range search"}
            )

        logger.info(f"Consultation search request: query='{query}', type='{search_type}', date_from='{date_from}', date_to='{date_to}'")

        # Build SQL query based on search type
        if search_type == 'patient_name':
            sql_query = """
            SELECT cp.ID_probleme as consultation_id,
                   cp.DateConsultation::date as consultation_date,
                   cp.DateConsultation::time as consultation_time,
                   p.ID_patient as patient_id,
                   CONCAT(p.Prenom, ' ', p.Nom) as patient_name,
                   m.ID_medecin as doctor_id,
                   CONCAT(m.Prenom, ' ', m.Nom) as doctor_name,
                   m.Specialite as specialty,
                   cp.LibelleProbleme as diagnosis
            FROM Consult_prob cp
            JOIN Patients p ON cp.ID_patient = p.ID_patient
            JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            WHERE LOWER(CONCAT(p.Prenom, ' ', p.Nom)) LIKE LOWER(%s)
            ORDER BY cp.DateConsultation DESC
            LIMIT 50
            """
            params = [f"%{query}%"]

        elif search_type == 'doctor_name':
            sql_query = """
            SELECT cp.ID_probleme as consultation_id,
                   cp.DateConsultation::date as consultation_date,
                   cp.DateConsultation::time as consultation_time,
                   p.ID_patient as patient_id,
                   CONCAT(p.Prenom, ' ', p.Nom) as patient_name,
                   m.ID_medecin as doctor_id,
                   CONCAT(m.Prenom, ' ', m.Nom) as doctor_name,
                   m.Specialite as specialty,
                   cp.LibelleProbleme as diagnosis
            FROM Consult_prob cp
            JOIN Patients p ON cp.ID_patient = p.ID_patient
            JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            WHERE LOWER(CONCAT(m.Prenom, ' ', m.Nom)) LIKE LOWER(%s)
            ORDER BY cp.DateConsultation DESC
            LIMIT 50
            """
            params = [f"%{query}%"]

        elif search_type == 'specialty':
            sql_query = """
            SELECT cp.ID_probleme as consultation_id,
                   cp.DateConsultation::date as consultation_date,
                   cp.DateConsultation::time as consultation_time,
                   p.ID_patient as patient_id,
                   CONCAT(p.Prenom, ' ', p.Nom) as patient_name,
                   m.ID_medecin as doctor_id,
                   CONCAT(m.Prenom, ' ', m.Nom) as doctor_name,
                   m.Specialite as specialty,
                   cp.LibelleProbleme as diagnosis
            FROM Consult_prob cp
            JOIN Patients p ON cp.ID_patient = p.ID_patient
            JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            WHERE LOWER(m.Specialite) LIKE LOWER(%s)
            ORDER BY cp.DateConsultation DESC
            LIMIT 50
            """
            params = [f"%{query}%"]

        elif search_type == 'diagnosis':
            sql_query = """
            SELECT cp.ID_probleme as consultation_id,
                   cp.DateConsultation::date as consultation_date,
                   cp.DateConsultation::time as consultation_time,
                   p.ID_patient as patient_id,
                   CONCAT(p.Prenom, ' ', p.Nom) as patient_name,
                   m.ID_medecin as doctor_id,
                   CONCAT(m.Prenom, ' ', m.Nom) as doctor_name,
                   m.Specialite as specialty,
                   cp.LibelleProbleme as diagnosis
            FROM Consult_prob cp
            JOIN Patients p ON cp.ID_patient = p.ID_patient
            JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            WHERE LOWER(cp.LibelleProbleme) LIKE LOWER(%s)
            ORDER BY cp.DateConsultation DESC
            LIMIT 50
            """
            params = [f"%{query}%"]

        elif search_type == 'consultation_id':
            sql_query = """
            SELECT cp.ID_probleme as consultation_id,
                   cp.DateConsultation::date as consultation_date,
                   cp.DateConsultation::time as consultation_time,
                   p.ID_patient as patient_id,
                   CONCAT(p.Prenom, ' ', p.Nom) as patient_name,
                   m.ID_medecin as doctor_id,
                   CONCAT(m.Prenom, ' ', m.Nom) as doctor_name,
                   m.Specialite as specialty,
                   cp.LibelleProbleme as diagnosis
            FROM Consult_prob cp
            JOIN Patients p ON cp.ID_patient = p.ID_patient
            JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            WHERE cp.ID_probleme = %s
            ORDER BY cp.DateConsultation DESC
            """
            try:
                consultation_id = int(query)
                params = [consultation_id]
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail={"error": "Consultation ID must be a number"}
                )

        elif search_type == 'date_range':
            sql_query = """
            SELECT cp.ID_probleme as consultation_id,
                   cp.DateConsultation::date as consultation_date,
                   cp.DateConsultation::time as consultation_time,
                   p.ID_patient as patient_id,
                   CONCAT(p.Prenom, ' ', p.Nom) as patient_name,
                   m.ID_medecin as doctor_id,
                   CONCAT(m.Prenom, ' ', m.Nom) as doctor_name,
                   m.Specialite as specialty,
                   cp.LibelleProbleme as diagnosis
            FROM Consult_prob cp
            JOIN Patients p ON cp.ID_patient = p.ID_patient
            JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            WHERE cp.DateConsultation::date BETWEEN %s AND %s
            ORDER BY cp.DateConsultation DESC
            LIMIT 100
            """
            params = [date_from, date_to]

        else:
            raise HTTPException(
                status_code=400,
                detail={"error": f"Invalid search type: {search_type}"}
            )

        # Execute the query - format SQL with parameters
        if search_type == 'consultation_id':
            formatted_sql = sql_query.replace('%s', str(params[0]))
        elif search_type == 'date_range':
            formatted_sql = sql_query.replace('%s', f"'{params[0]}'", 1).replace('%s', f"'{params[1]}'", 1)
        else:
            formatted_sql = sql_query.replace('%s', f"'{params[0]}'")

        df = vn.run_sql(formatted_sql)

        if df is None or df.empty:
            logger.info(f"No consultations found for query '{query}' (type: {search_type})")
            return {
                "status": "success",
                "results": [],
                "total": 0,
                "query": query,
                "search_type": search_type
            }

        # Convert DataFrame to list of dictionaries
        consultation_results = []
        for _, row in df.iterrows():
            consultation_results.append({
                "consultation_id": row.get('consultation_id', ''),
                "consultation_date": str(row.get('consultation_date', '')) if row.get('consultation_date') else None,
                "consultation_time": str(row.get('consultation_time', '')) if row.get('consultation_time') else None,
                "patient_id": row.get('patient_id', ''),
                "patient_name": row.get('patient_name', ''),
                "doctor_id": row.get('doctor_id', ''),
                "doctor_name": row.get('doctor_name', ''),
                "specialty": row.get('specialty', ''),
                "diagnosis": row.get('diagnosis', '')
            })

        logger.info(f"Consultation search completed. Found {len(consultation_results)} results for query '{query}' (type: {search_type})")

        return {
            "status": "success",
            "results": consultation_results,
            "total": len(consultation_results),
            "query": query,
            "search_type": search_type
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error in consultation search: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail={"error": "Search failed. Please try again."}
        )

@router.post("/search/doctors")
async def search_doctors(
    search_request: DoctorSearchRequest,
    request: Request,
    vn = Depends(get_vanna)
):
    """Search for doctors based on different criteria"""
    try:
        query = search_request.query.strip()
        search_type = search_request.type

        if not query:
            raise HTTPException(
                status_code=400,
                detail={"error": "Search query cannot be empty"}
            )

        logger.info(f"Doctor search request: query='{query}', type='{search_type}'")

        # Build SQL query based on search type
        if search_type == 'name':
            sql_query = """
            SELECT ID_medecin as doctor_id, Prenom as first_name, Nom as last_name,
                   Specialite as specialty, Specialite as department,
                   '' as license_number, '' as phone, Email as email, 'active' as status
            FROM Medecins
            WHERE LOWER(CONCAT(Prenom, ' ', Nom)) LIKE LOWER(%s)
            ORDER BY Nom, Prenom
            LIMIT 50
            """
            params = [f"%{query}%"]

        elif search_type == 'specialty':
            sql_query = """
            SELECT ID_medecin as doctor_id, Prenom as first_name, Nom as last_name,
                   Specialite as specialty, Specialite as department,
                   '' as license_number, '' as phone, Email as email, 'active' as status
            FROM Medecins
            WHERE LOWER(Specialite) LIKE LOWER(%s)
            ORDER BY Specialite, Nom, Prenom
            LIMIT 50
            """
            params = [f"%{query}%"]

        elif search_type == 'department':
            sql_query = """
            SELECT ID_medecin as doctor_id, Prenom as first_name, Nom as last_name,
                   Specialite as specialty, Specialite as department,
                   '' as license_number, '' as phone, Email as email, 'active' as status
            FROM Medecins
            WHERE LOWER(Specialite) LIKE LOWER(%s)
            ORDER BY Specialite, Nom, Prenom
            LIMIT 50
            """
            params = [f"%{query}%"]

        elif search_type == 'license':
            # Since license_number doesn't exist in Medecins table, search by ID instead
            sql_query = """
            SELECT ID_medecin as doctor_id, Prenom as first_name, Nom as last_name,
                   Specialite as specialty, Specialite as department,
                   CAST(ID_medecin AS VARCHAR) as license_number, '' as phone, Email as email, 'active' as status
            FROM Medecins
            WHERE CAST(ID_medecin AS VARCHAR) LIKE %s
            ORDER BY Nom, Prenom
            LIMIT 50
            """
            params = [f"%{query}%"]

        elif search_type == 'email':
            sql_query = """
            SELECT ID_medecin as doctor_id, Prenom as first_name, Nom as last_name,
                   Specialite as specialty, Specialite as department,
                   '' as license_number, '' as phone, Email as email, 'active' as status
            FROM Medecins
            WHERE LOWER(Email) LIKE LOWER(%s)
            ORDER BY Nom, Prenom
            LIMIT 50
            """
            params = [f"%{query}%"]

        elif search_type == 'doctor_id':
            sql_query = """
            SELECT ID_medecin as doctor_id, Prenom as first_name, Nom as last_name,
                   Specialite as specialty, Specialite as department,
                   '' as license_number, '' as phone, Email as email, 'active' as status
            FROM Medecins
            WHERE ID_medecin = %s
            """
            try:
                doctor_id = int(query)
                params = [doctor_id]
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail={"error": "Doctor ID must be a number"}
                )

        else:
            raise HTTPException(
                status_code=400,
                detail={"error": f"Invalid search type: {search_type}"}
            )

        # Execute the query - format SQL with parameters
        if search_type == 'doctor_id':
            formatted_sql = sql_query.replace('%s', str(params[0]))
        else:
            formatted_sql = sql_query.replace('%s', f"'{params[0]}'")

        df = vn.run_sql(formatted_sql)

        if df is None or df.empty:
            logger.info(f"No doctors found for query '{query}' (type: {search_type})")
            return {
                "status": "success",
                "results": [],
                "total": 0,
                "query": query,
                "search_type": search_type
            }

        # Debug: Log the DataFrame columns to see what's actually being returned
        logger.info(f"Doctor DataFrame columns: {list(df.columns)}")
        logger.info(f"Doctor DataFrame sample: {df.head().to_dict('records') if not df.empty else 'Empty'}")

        # Convert DataFrame to list of dictionaries
        doctor_results = []
        for _, row in df.iterrows():
            specialty = row.get('specialty', '')
            department = row.get('department', '') or specialty  # Use specialty as fallback for department

            # Debug: Log the row data to see what's actually being returned
            logger.info(f"Doctor row data: {dict(row)}")

            doctor_results.append({
                "doctor_id": row.get('doctor_id', ''),
                "first_name": row.get('first_name', ''),
                "last_name": row.get('last_name', ''),
                "specialty": specialty,
                "department": department,
                "license_number": row.get('license_number', ''),
                "phone": row.get('phone', ''),
                "email": row.get('email', ''),
                "status": row.get('status', 'active')
            })

        logger.info(f"Doctor search completed. Found {len(doctor_results)} results for query '{query}' (type: {search_type})")

        return {
            "status": "success",
            "results": doctor_results,
            "total": len(doctor_results),
            "query": query,
            "search_type": search_type
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error in doctor search: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail={"error": "Search failed. Please try again."}
        )

@router.get("/patients/{patient_id}/details")
async def get_patient_details(
    patient_id: int,
    request: Request,
    vn = Depends(get_vanna)
):
    """Get detailed information for a specific patient"""
    try:
        # Basic patient information
        patient_info_sql = """
            SELECT
                p.ID_patient as patient_id,
                p.Nom as last_name,
                p.Prenom as first_name,
                CONCAT(p.Prenom, ' ', p.Nom) as full_name,
                p.DateNaissance as date_of_birth,
                p.Sexe as gender,
                p.Telephone as phone,
                p.Adresse as address
            FROM Patients p
            WHERE p.ID_patient = %s
        """

        # Medical antecedents
        antecedents_sql = """
            SELECT
                a.ID_ATCD as id,
                a.Libelle as condition,
                a.Details as details
            FROM Antecedents a
            WHERE a.ID_patient = %s
            ORDER BY a.ID_ATCD
        """

        # Recent consultations
        consultations_sql = """
            SELECT
                cp.ID_probleme as consultation_id,
                cp.DateConsultation as date,
                cp.LibelleProbleme as diagnosis,
                CONCAT(m.Prenom, ' ', m.Nom) as doctor_name,
                m.Specialite as specialty
            FROM Consult_prob cp
            LEFT JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            WHERE cp.ID_patient = %s
            ORDER BY cp.DateConsultation DESC
            LIMIT 10
        """

        # Recent biometric data
        biometrics_sql = """
            SELECT
                b.DateMesure as date,
                b.Temperature as temperature,
                b.TensionSystolique as systolic_bp,
                b.TensionDiastolique as diastolic_bp,
                b.Pouls as pulse,
                b.SpO2 as spo2,
                b.FrequenceResp as respiratory_rate,
                b.Commentaire as comments
            FROM Biometrie b
            WHERE b.ID_patient = %s
            ORDER BY b.DateMesure DESC
            LIMIT 5
        """

        # Recent prescriptions
        prescriptions_sql = """
            SELECT DISTINCT
                pr.DatePrescription as date,
                m.Nom as medication_name,
                m.Forme as form,
                m.Dosage as dosage,
                cp.LibelleProbleme as prescribed_for
            FROM Prescriptions pr
            JOIN Medicaments m ON pr.ID_medicament = m.ID_medicament
            JOIN Consult_prob cp ON pr.ID_probleme = cp.ID_probleme
            WHERE cp.ID_patient = %s
            ORDER BY pr.DatePrescription DESC
            LIMIT 10
        """

        # Execute all queries
        patient_info_df = vn.run_sql(patient_info_sql.replace('%s', str(patient_id)))

        if patient_info_df.empty:
            raise HTTPException(
                status_code=404,
                detail={"error": "Patient not found"}
            )

        patient_info = patient_info_df.to_dict('records')[0]

        # Get additional data
        antecedents_df = vn.run_sql(antecedents_sql.replace('%s', str(patient_id)))
        consultations_df = vn.run_sql(consultations_sql.replace('%s', str(patient_id)))
        biometrics_df = vn.run_sql(biometrics_sql.replace('%s', str(patient_id)))
        prescriptions_df = vn.run_sql(prescriptions_sql.replace('%s', str(patient_id)))

        # Convert to lists
        antecedents = antecedents_df.to_dict('records') if not antecedents_df.empty else []
        consultations = consultations_df.to_dict('records') if not consultations_df.empty else []
        biometrics = biometrics_df.to_dict('records') if not biometrics_df.empty else []
        prescriptions = prescriptions_df.to_dict('records') if not prescriptions_df.empty else []

        return {
            "status": "success",
            "data": {
                "patient_info": patient_info,
                "antecedents": antecedents,
                "consultations": consultations,
                "biometrics": biometrics,
                "prescriptions": prescriptions
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting patient details: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail={"error": f"Internal server error: {str(e)}"}
        )

@router.get("/consultations/{consultation_id}/details")
async def get_consultation_details(
    consultation_id: int,
    request: Request,
    vn = Depends(get_vanna)
):
    """Get detailed information for a specific consultation"""
    try:
        # Basic consultation information
        consultation_info_sql = """
            SELECT
                cp.ID_probleme as consultation_id,
                cp.DateConsultation as consultation_date,
                cp.LibelleProbleme as diagnosis,
                cp.ID_patient as patient_id,
                CONCAT(p.Prenom, ' ', p.Nom) as patient_name,
                cp.ID_medecin as doctor_id,
                CONCAT(m.Prenom, ' ', m.Nom) as doctor_name,
                m.Specialite as doctor_specialty,
                m.Email as doctor_email
            FROM Consult_prob cp
            JOIN Patients p ON cp.ID_patient = p.ID_patient
            JOIN Medecins m ON cp.ID_medecin = m.ID_medecin
            WHERE cp.ID_probleme = %s
        """

        # Symptoms for this consultation
        symptoms_sql = """
            SELECT
                s.ID_symptome as symptom_id,
                s.Libelle as symptom_name
            FROM Symptomes s
            WHERE s.ID_probleme = %s
            ORDER BY s.ID_symptome
        """

        # Observations for this consultation
        observations_sql = """
            SELECT
                o.ID_observation as observation_id,
                o.Observation as observation_text,
                o.DateObservation as observation_date
            FROM Observations o
            WHERE o.ID_probleme = %s
            ORDER BY o.DateObservation DESC
        """

        # Biometric data around consultation date
        biometrics_sql = """
            SELECT
                b.DateMesure as date,
                b.Temperature as temperature,
                b.TensionSystolique as systolic_bp,
                b.TensionDiastolique as diastolic_bp,
                b.Pouls as pulse,
                b.SpO2 as spo2,
                b.FrequenceResp as respiratory_rate,
                b.Commentaire as comments
            FROM Biometrie b
            JOIN Consult_prob cp ON b.ID_patient = cp.ID_patient
            WHERE cp.ID_probleme = %s
            AND ABS(EXTRACT(EPOCH FROM (b.DateMesure - cp.DateConsultation))) <= 86400
            ORDER BY ABS(EXTRACT(EPOCH FROM (b.DateMesure - cp.DateConsultation)))
            LIMIT 5
        """

        # Prescriptions for this consultation
        prescriptions_sql = """
            SELECT
                pr.DatePrescription as date,
                m.Nom as medication_name,
                m.Forme as form,
                m.Dosage as dosage
            FROM Prescriptions pr
            JOIN Medicaments m ON pr.ID_medicament = m.ID_medicament
            WHERE pr.ID_probleme = %s
            ORDER BY pr.DatePrescription
        """

        # Procedures for this consultation
        procedures_sql = """
            SELECT
                ps.DateProcedure as date,
                ps.TypeProcedure as procedure_type,
                ps.Libelle as procedure_name
            FROM Procedures_soins ps
            WHERE ps.ID_probleme = %s
            ORDER BY ps.DateProcedure
        """

        # Execute all queries
        consultation_info_df = vn.run_sql(consultation_info_sql.replace('%s', str(consultation_id)))

        if consultation_info_df.empty:
            raise HTTPException(
                status_code=404,
                detail={"error": "Consultation not found"}
            )

        consultation_info = consultation_info_df.to_dict('records')[0]

        # Get additional data
        symptoms_df = vn.run_sql(symptoms_sql.replace('%s', str(consultation_id)))
        observations_df = vn.run_sql(observations_sql.replace('%s', str(consultation_id)))
        biometrics_df = vn.run_sql(biometrics_sql.replace('%s', str(consultation_id)))
        prescriptions_df = vn.run_sql(prescriptions_sql.replace('%s', str(consultation_id)))
        procedures_df = vn.run_sql(procedures_sql.replace('%s', str(consultation_id)))

        # Convert to lists
        symptoms = symptoms_df.to_dict('records') if not symptoms_df.empty else []
        observations = observations_df.to_dict('records') if not observations_df.empty else []
        biometrics = biometrics_df.to_dict('records') if not biometrics_df.empty else []
        prescriptions = prescriptions_df.to_dict('records') if not prescriptions_df.empty else []
        procedures = procedures_df.to_dict('records') if not procedures_df.empty else []

        return {
            "status": "success",
            "data": {
                "consultation_info": consultation_info,
                "symptoms": symptoms,
                "observations": observations,
                "biometrics": biometrics,
                "prescriptions": prescriptions,
                "procedures": procedures
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting consultation details: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail={"error": f"Internal server error: {str(e)}"}
        )

@router.get("/doctors/{doctor_id}/details")
async def get_doctor_details(
    doctor_id: int,
    request: Request,
    vn = Depends(get_vanna)
):
    """Get detailed information for a specific doctor"""
    try:
        # Basic doctor information
        doctor_info_sql = """
            SELECT
                m.ID_medecin as doctor_id,
                m.Nom as last_name,
                m.Prenom as first_name,
                CONCAT(m.Prenom, ' ', m.Nom) as full_name,
                m.Specialite as specialty,
                m.Email as email
            FROM Medecins m
            WHERE m.ID_medecin = %s
        """

        # Recent consultations by this doctor
        consultations_sql = """
            SELECT
                cp.ID_probleme as consultation_id,
                cp.DateConsultation as date,
                cp.LibelleProbleme as diagnosis,
                CONCAT(p.Prenom, ' ', p.Nom) as patient_name,
                p.ID_patient as patient_id
            FROM Consult_prob cp
            JOIN Patients p ON cp.ID_patient = p.ID_patient
            WHERE cp.ID_medecin = %s
            ORDER BY cp.DateConsultation DESC
            LIMIT 20
        """

        # Patients treated by this doctor
        patients_sql = """
            SELECT DISTINCT
                p.ID_patient as patient_id,
                CONCAT(p.Prenom, ' ', p.Nom) as patient_name,
                COUNT(cp.ID_probleme) as consultation_count,
                MAX(cp.DateConsultation) as last_consultation
            FROM Patients p
            JOIN Consult_prob cp ON p.ID_patient = cp.ID_patient
            WHERE cp.ID_medecin = %s
            GROUP BY p.ID_patient, p.Prenom, p.Nom
            ORDER BY MAX(cp.DateConsultation) DESC
            LIMIT 15
        """

        # Practice statistics
        statistics_sql = """
            SELECT
                COUNT(DISTINCT cp.ID_probleme) as total_consultations,
                COUNT(DISTINCT cp.ID_patient) as total_patients,
                COUNT(DISTINCT DATE(cp.DateConsultation)) as active_days,
                MIN(cp.DateConsultation) as first_consultation,
                MAX(cp.DateConsultation) as last_consultation
            FROM Consult_prob cp
            WHERE cp.ID_medecin = %s
        """

        # Execute all queries
        doctor_info_df = vn.run_sql(doctor_info_sql.replace('%s', str(doctor_id)))

        if doctor_info_df.empty:
            raise HTTPException(
                status_code=404,
                detail={"error": "Doctor not found"}
            )

        doctor_info = doctor_info_df.to_dict('records')[0]

        # Get additional data
        consultations_df = vn.run_sql(consultations_sql.replace('%s', str(doctor_id)))
        patients_df = vn.run_sql(patients_sql.replace('%s', str(doctor_id)))
        statistics_df = vn.run_sql(statistics_sql.replace('%s', str(doctor_id)))

        # Convert to lists
        consultations = consultations_df.to_dict('records') if not consultations_df.empty else []
        patients = patients_df.to_dict('records') if not patients_df.empty else []
        statistics = statistics_df.to_dict('records')[0] if not statistics_df.empty else {}

        return {
            "status": "success",
            "data": {
                "doctor_info": doctor_info,
                "consultations": consultations,
                "patients": patients,
                "statistics": statistics
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting doctor details: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail={"error": f"Internal server error: {str(e)}"}
        )

@router.get("/search/test")
async def test_search_endpoint():
    """Test endpoint to verify search API is working"""
    return {
        "status": "success",
        "message": "Search API is working",
        "available_endpoints": ["/search/patients", "/search/consultations", "/search/doctors", "/patients/{patient_id}/details", "/consultations/{consultation_id}/details", "/doctors/{doctor_id}/details"]
    }
