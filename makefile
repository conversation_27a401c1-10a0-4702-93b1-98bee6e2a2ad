# Hospital Assistant System Makefile
# Provides convenient commands for development
# Compatible with both Windows and Linux/macOS

# Detect operating system
ifeq ($(OS),Windows_NT)
    DETECTED_OS := Windows
    VENV_ACTIVATE = .venv\Scripts\activate
    VENV_PYTHON = .venv\Scripts\python
    CD_CMD = cd /d
    CLEAN_PYCACHE = powershell -Command "Get-ChildItem -Path . -Recurse -Name '__pycache__' -Directory | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue"
    CLEAN_PYC = powershell -Command "Get-ChildItem -Path . -Recurse -Name '*.pyc' -File | Remove-Item -Force -ErrorAction SilentlyContinue"
else
    DETECTED_OS := $(shell uname -s)
    VENV_ACTIVATE = .venv/bin/activate
    VENV_PYTHON = .venv/bin/python
    CD_CMD = cd
    CLEAN_PYCACHE = find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    CLEAN_PYC = find . -type f -name "*.pyc" -delete 2>/dev/null || true
endif

.PHONY: help be fe install-be install-fe install clean detect-os

# Default target
help:
	@echo "🏥 Hospital Assistant System - Development Commands"
	@echo "=================================================="
	@echo "🖥️  Detected OS: $(DETECTED_OS)"
	@echo ""
	@echo "Available commands:"
	@echo "  make be         - Start the FastAPI backend server"
	@echo "  make fe         - Start the React frontend server"
	@echo "  make install-be - Install Python backend dependencies"
	@echo "  make install-fe - Install Node.js frontend dependencies"
	@echo "  make install    - Install all dependencies (backend + frontend)"
	@echo "  make clean      - Clean cache and temporary files"
	@echo "  make detect-os  - Show detected operating system"
	@echo "  make help       - Show this help message"
	@echo ""

# Utility command to show detected OS
detect-os:
	@echo "🖥️  Detected operating system: $(DETECTED_OS)"

# Backend commands
be:
	@echo "🚀 Starting FastAPI backend server..."
ifeq ($(OS),Windows_NT)
	@$(VENV_ACTIVATE) && $(CD_CMD) backend && python run_fastapi.py
else
	@. $(VENV_ACTIVATE) && $(CD_CMD) backend && python run_fastapi.py
endif

install-be:
	@echo "📦 Creating Python virtual environment and installing dependencies..."
	@uv venv --python=3.11
	@echo "📦 Syncing dependencies with uv.lock..."
	@uv sync

# Frontend commands
fe:
	@echo "⚛️ Starting React frontend server..."
	@$(CD_CMD) frontend && npm start

install-fe:
	@echo "📦 Installing Node.js frontend dependencies..."
	@$(CD_CMD) frontend && npm install

# Combined commands
install: install-be install-fe
	@echo "✅ All dependencies installed successfully!"

# Utility commands
clean:
	@echo "🧹 Cleaning cache and temporary files..."
	@$(CLEAN_PYCACHE)
	@$(CLEAN_PYC)
	@echo "✅ Cleanup completed!"

