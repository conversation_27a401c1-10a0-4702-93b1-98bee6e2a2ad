import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import i18n from '../../locales/i18n';


const Header = () => {
  const { theme, toggleTheme } = useTheme();
  const { user, logout } = useAuth();
  const { t, i18n: i18nextInstance } = useTranslation();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="bg-white dark:bg-gray-800 px-6 py-4 flex justify-between items-center border-b border-gray-200 dark:border-gray-700 shadow-sm transition-colors duration-300">
      <span className="text-xl font-semibold text-gray-900 dark:text-gray-100">
        {t('header.title', { defaultValue: 'HOSPITAL ASSISTANT SYSTEM' })}
      </span>

      <div className="flex items-center space-x-5">
        {/* Language Options */}
        <div className="hidden md:flex items-center space-x-2 text-sm">
          <button
            onClick={() => i18n.changeLanguage('en')}
            className={`transition-colors text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 ${i18nextInstance.language === 'en' ? 'font-bold underline text-blue-600 dark:text-blue-400' : ''}`}
            aria-label="Switch to English"
          >
            <i className="fas fa-globe mr-1"></i> {t('header.language.en', { defaultValue: 'EN' })}
          </button>
          <span className="text-gray-400">|</span>
          <button
            onClick={() => i18n.changeLanguage('fr')}
            className={`transition-colors text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 ${i18nextInstance.language === 'fr' ? 'font-bold underline text-blue-600 dark:text-blue-400' : ''}`}
            aria-label="Switch to French"
          >
            <i className="fas fa-globe mr-1"></i> {t('header.language.fr', { defaultValue: 'FR' })}
          </button>
        </div>

        {/* User Info */}
        <span className="text-gray-700 dark:text-gray-300 text-sm">
          <i className="fas fa-user-md mr-2"></i>
          {user?.name || t('header.defaultUser', { defaultValue: 'Dr. Leblanc' })}
        </span>

        {/* Logout */}
        <button
          onClick={handleLogout}
          className="text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors text-sm"
          aria-label={t('header.logout', { defaultValue: 'Logout' })}
        >
          <i className="fas fa-sign-out-alt mr-1"></i>
          {t('header.logout', { defaultValue: 'Logout' })}
        </button>

        {/* Theme Toggle */}
        <button
          onClick={toggleTheme}
          className="flex items-center text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors text-sm"
          aria-label={t('header.toggleTheme', { defaultValue: 'Toggle Theme' })}
        >
          <i className={`fas ${theme === 'dark' ? 'fa-sun' : 'fa-moon'} mr-2`}></i>
          <span className="hidden sm:inline">{t('header.toggleTheme', { defaultValue: 'Toggle Theme' })}</span>
        </button>
      </div>
    </div>
  );
};

export default Header;
