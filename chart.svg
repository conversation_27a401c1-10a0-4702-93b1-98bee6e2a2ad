<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1059.029541015625 1226.9530029296875" style="max-width: 1059.029541015625px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="export-svg"><style xmlns="http://www.w3.org/1999/xhtml">@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"); p {margin: 0;}</style><style>#export-svg{font-family:arial,sans-serif;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#export-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#export-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#export-svg .error-icon{fill:#ffffff;}#export-svg .error-text{fill:#000000;stroke:#000000;}#export-svg .edge-thickness-normal{stroke-width:2px;}#export-svg .edge-thickness-thick{stroke-width:3.5px;}#export-svg .edge-pattern-solid{stroke-dasharray:0;}#export-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#export-svg .edge-pattern-dashed{stroke-dasharray:3;}#export-svg .edge-pattern-dotted{stroke-dasharray:2;}#export-svg .marker{fill:#000000;stroke:#000000;}#export-svg .marker.cross{stroke:#000000;}#export-svg svg{font-family:arial,sans-serif;font-size:14px;}#export-svg p{margin:0;}#export-svg .label{font-family:arial,sans-serif;color:#333;}#export-svg .cluster-label text{fill:#000000;}#export-svg .cluster-label span{color:#000000;}#export-svg .cluster-label span p{background-color:transparent;}#export-svg .label text,#export-svg span{fill:#333;color:#333;}#export-svg .node rect,#export-svg .node circle,#export-svg .node ellipse,#export-svg .node polygon,#export-svg .node path{fill:#ffffff;stroke:#000000;stroke-width:2px;}#export-svg .rough-node .label text,#export-svg .node .label text,#export-svg .image-shape .label,#export-svg .icon-shape .label{text-anchor:middle;}#export-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#export-svg .rough-node .label,#export-svg .node .label,#export-svg .image-shape .label,#export-svg .icon-shape .label{text-align:center;}#export-svg .node.clickable{cursor:pointer;}#export-svg .root .anchor path{fill:#000000!important;stroke-width:0;stroke:#000000;}#export-svg .arrowheadPath{fill:#000000;}#export-svg .edgePath .path{stroke:#000000;stroke-width:2px;}#export-svg .flowchart-link{stroke:#000000;fill:none;}#export-svg .edgeLabel{background-color:hsl(-120, 0%, 80%);text-align:center;}#export-svg .edgeLabel p{background-color:hsl(-120, 0%, 80%);}#export-svg .edgeLabel rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#export-svg .labelBkg{background-color:rgba(204, 204, 204, 0.5);}#export-svg .cluster rect{fill:#ffffff;stroke:hsl(0, 0%, 90%);stroke-width:2px;}#export-svg .cluster text{fill:#000000;}#export-svg .cluster span{color:#000000;}#export-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:arial,sans-serif;font-size:12px;background:#ffffff;border:1px solid hsl(0, 0%, 90%);border-radius:2px;pointer-events:none;z-index:100;}#export-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#export-svg rect.text{fill:none;stroke-width:0;}#export-svg .icon-shape,#export-svg .image-shape{background-color:hsl(-120, 0%, 80%);text-align:center;}#export-svg .icon-shape p,#export-svg .image-shape p{background-color:hsl(-120, 0%, 80%);padding:2px;}#export-svg .icon-shape rect,#export-svg .image-shape rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#export-svg .node .neo-node{stroke:#000000;}#export-svg [data-look="neo"].node rect,#export-svg [data-look="neo"].cluster rect,#export-svg [data-look="neo"].node polygon{stroke:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].node path{stroke:url(#export-svg-gradient);stroke-width:2;}#export-svg [data-look="neo"].node .outer-path{filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].node .neo-line path{stroke:#000000;filter:none;}#export-svg [data-look="neo"].node circle{stroke:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].node circle .state-start{fill:#000000;}#export-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:url(#export-svg-gradient);stroke-width:2;}#export-svg [data-look="neo"].icon-shape .icon{fill:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg [data-look="neo"].icon-shape .icon-neo path{stroke:url(#export-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#export-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#export-svg .client&gt;*{fill:#e6e6fa!important;stroke:#333!important;stroke-width:2px!important;}#export-svg .client span{fill:#e6e6fa!important;stroke:#333!important;stroke-width:2px!important;}#export-svg .backend&gt;*{fill:#add8e6!important;stroke:#333!important;stroke-width:2px!important;}#export-svg .backend span{fill:#add8e6!important;stroke:#333!important;stroke-width:2px!important;}#export-svg .vanna_engine&gt;*{fill:#90ee90!important;stroke:#333!important;stroke-width:2px!important;}#export-svg .vanna_engine span{fill:#90ee90!important;stroke:#333!important;stroke-width:2px!important;}#export-svg .external_ai&gt;*{fill:#ffcc99!important;stroke:#333!important;stroke-width:2px!important;}#export-svg .external_ai span{fill:#ffcc99!important;stroke:#333!important;stroke-width:2px!important;}#export-svg .storage&gt;*{fill:#lightyellow!important;stroke:#333!important;stroke-width:2px!important;}#export-svg .storage span{fill:#lightyellow!important;stroke:#333!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="7.75" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointEnd"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="4" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointStart"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="11.5" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointEnd-margin"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="1" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="export-svg_flowchart-v2-pointStart-margin"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="10.75" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleStart"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="12.25" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleEnd-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="-2" viewBox="0 0 10 10" class="marker flowchart-v2" id="export-svg_flowchart-v2-circleStart-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossStart"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossEnd-margin"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="export-svg_flowchart-v2-crossStart-margin"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><g class="root"><g class="clusters"><g data-look="neo" data-et="cluster" data-id="Data_Sources_And_Cache" id="Data_Sources_And_Cache" class="cluster"><rect height="136.99478912353516" width="687.0313262939453" y="1081.958339691162" x="363.99829864501953" style=""/><g transform="translate(622.6788864135742, 1081.958339691162)" class="cluster-label"><foreignObject height="20.99826431274414" width="169.67015075683594"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Data_Sources_And_Cache</p></span></div></foreignObject></g></g><g data-look="neo" data-et="cluster" data-id="External_AI_Services" id="External_AI_Services" class="cluster"><rect height="136.99478912353516" width="386.99829864501953" y="894.963550567627" x="8" style=""/><g transform="translate(134.5764045715332, 894.963550567627)" class="cluster-label"><foreignObject height="20.99826431274414" width="133.84548950195312"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>External_AI_Services</p></span></div></foreignObject></g></g><g data-look="neo" data-et="cluster" data-id="Vanna_Engine" id="Vanna_Engine" class="cluster"><rect height="283.98265075683594" width="730.5269927978516" y="518.9843711853027" x="63.50000762939453" style=""/><g transform="translate(377.1272163391113, 518.9843711853027)" class="cluster-label"><foreignObject height="20.99826431274414" width="103.27257537841797"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Vanna AI Engine</p></span></div></foreignObject></g></g><g data-look="neo" data-et="cluster" data-id="Application_Backend" id="Application_Backend" class="cluster"><rect height="439.9861068725586" width="681.5313186645508" y="8" x="369.49830627441406" style=""/><g transform="translate(657.7335815429688, 8)" class="cluster-label"><foreignObject height="20.99826431274414" width="105.0607681274414"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Flask Application</p></span></div></foreignObject></g></g><g data-look="neo" data-et="cluster" data-id="User_Interface" id="User_Interface" class="cluster"><rect height="136.99478912353516" width="302.0000305175781" y="127.99826431274414" x="47.49827575683594" style=""/><g transform="translate(155.31252670288086, 127.99826431274414)" class="cluster-label"><foreignObject height="20.99826431274414" width="86.37152862548828"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Client-Side UI</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTk4LjQ5ODI5MTAxNTYyNSwieSI6NzcuOTk4MjY0MzEyNzQ0MTR9LHsieCI6MTk4LjQ5ODI5MTAxNTYyNSwieSI6MTAyLjk5ODI2NDMxMjc0NDE0fSx7IngiOjE5OC40OTgyOTEwMTU2MjUsInkiOjEyNy45OTgyNjQzMTI3NDQxNH0seyJ4IjoxOTguNDk4MjkxMDE1NjI1LCJ5IjoxNTIuOTk4MjY0MzEyNzQ0MTR9XQ==" data-id="L_User_WebAppUI_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 62 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_User_WebAppUI_0" d="M198.498291015625,77.99826431274414L198.498291015625,102.99826431274414L198.498291015625,127.99826431274414L198.498291015625,148.99826431274414"/><path data-points="W3sieCI6OTA3LjU1NTY2NDA2MjUsInkiOjc3Ljk5ODI2NDMxMjc0NDE0fSx7IngiOjkwNy41NTU2NjQwNjI1LCJ5IjoxMDIuOTk4MjY0MzEyNzQ0MTR9LHsieCI6OTA3LjU1NTY2NDA2MjUsInkiOjEyNy45OTgyNjQzMTI3NDQxNH0seyJ4Ijo5MDcuNTU1NjY0MDYyNSwieSI6MTYzLjQ5NzM5NDU2MTc2NzU4fV0=" data-id="L_AppConfig_FlaskCore_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 0; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AppConfig_FlaskCore_0" d="M907.5556640625,77.99826431274414L907.5556640625,102.99826431274414L907.5556640625,127.99826431274414L907.5556640625,163.49739456176758"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODg3Ljk2NTU0MzA5MjE4MDIsInkiOjIyOS40OTM5MjMxODcyNTU4Nn0seyJ4Ijo4NjYuODkwNzI5OTA0MTc0OCwieSI6MjY0Ljk5MzA1MzQzNjI3OTN9LHsieCI6ODY2Ljg5MDcyOTkwNDE3NDgsInkiOjMwMC40OTIxODU1OTI2NTEzN30seyJ4Ijo3NTkuODQ4MTgwNzcwODc0LCJ5IjozMzguNDA0MTg1MTA1NTQxMDV9XQ==" data-id="L_FlaskCore_ApiEndpoints_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 174.4254608154297 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FlaskCore_ApiEndpoints_0" d="M887.9655430921802,229.49392318725586L875.9516826461739,249.73047726542427Q866.8907299041748,264.9930534362793 866.8907299041748,282.74261951446533L866.8907299041748,291.8285365362656Q866.8907299041748,300.49218559265137 858.7241647650596,303.38459441589936L763.6186775146662,337.06876221182813"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MjQyLjI0NDMwODEzMjEyNDcsInkiOjIzOS45OTMwNTM0MzYyNzkzfSx7IngiOjI2Ny4zODcyMDMyMTY1NTI3MywieSI6MjY0Ljk5MzA1MzQzNjI3OTN9LHsieCI6NjY4LjE3NDU1ODYzOTUyNjQsInkiOjMwMC40OTIxODU1OTI2NTEzN30seyJ4Ijo2NTcuMjQyODY0Nzk5MzY0LCJ5IjozMzUuOTkxMzE3NzQ5MDIzNDR9XQ==" data-id="L_WebAppUI_ApiEndpoints_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 457.7640075683594 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebAppUI_ApiEndpoints_0" d="M242.2443081321247,239.9930534362793L256.9643402712453,254.62942694228545Q267.38720321655273,264.9930534362793 282.0282114678457,266.2898585369958L661.7683013646147,299.9247610705134Q668.1745586395264,300.49218559265137 666.2817881441889,306.63869051111215L658.420082120977,332.16847072387395"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTM4Ljg3MjUyODM4MTQzMDYsInkiOjMzNS45OTEzMTc3NDkwMjM0NH0seyJ4Ijo0NTMuMTk5NzE4NDc1MzQxOCwieSI6MzAwLjQ5MjE4NTU5MjY1MTM3fSx7IngiOjE0Ni4zODcxODc5NTc3NjM2NywieSI6MjY0Ljk5MzA1MzQzNjI3OTN9LHsieCI6MTY1LjQwNjU2MjEyMjAxMzg0LCJ5IjoyMzkuOTkzMDUzNDM2Mjc5M31d" data-id="L_ApiEndpoints_WebAppUI_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 415.9464416503906 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApiEndpoints_WebAppUI_0" d="M538.8725283814306,335.99131774902344L486.58241724391826,314.32454193325776Q453.1997184753418,300.49218559265137 417.30418376677693,296.338964313542L152.1034294480076,265.65443976153006Q146.38718795776367,264.9930534362793 149.87131415513517,260.4133467549727L162.98466552839955,243.17651332920076"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjQ1LjY3NzcxODA3NTEwNjYsInkiOjQyMi45ODYxMDY4NzI1NTg2fSx7IngiOjY0Ni43MjkyNDgwNDY4NzUsInkiOjQ0Ny45ODYxMDY4NzI1NTg2fSx7IngiOjY0Ni43MjkyNDgwNDY4NzUsInkiOjQ4My40ODUyMzkwMjg5MzA2Nn0seyJ4Ijo2NDYuNzI5MjQ4MDQ2ODc1LCJ5Ijo1MTguOTg0MzcxMTg1MzAyN30seyJ4Ijo2MjYuMjAyNzU0NTUxMzU4MSwieSI6NTQzLjk4NDM3MTE4NTMwMjd9XQ==" data-id="L_ApiEndpoints_MyVanna_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 114.26513671875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApiEndpoints_MyVanna_0" d="M645.6777180751066,422.9861068725586L646.2034830609907,435.4861068725586Q646.729248046875,447.9861068725586 646.729248046875,460.4971591392699L646.729248046875,483.48523902893066L646.729248046875,504.81080528889714Q646.729248046875,518.9843711853027 637.7351396961153,529.938639083914L628.7410313453555,540.8929069825253"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDMzLjUzMjYwNTI0MTc3MTY1LCJ5Ijo1NDMuOTg0MzcxMTg1MzAyN30seyJ4Ijo0MTIuODg3MjEwODQ1OTQ3MjcsInkiOjUxOC45ODQzNzExODUzMDI3fSx7IngiOjQxMi44ODcyMTA4NDU5NDcyNywieSI6NDgzLjQ4NTIzOTAyODkzMDY2fSx7IngiOjQxMi44ODcyMTA4NDU5NDcyNywieSI6NDQ3Ljk4NjEwNjg3MjU1ODZ9LHsieCI6NTI3Ljg0ODE1MDI1MzI5NTksInkiOjQxMy44OTE0OTE4ODgxMzYxNn1d" data-id="L_MyVanna_ApiEndpoints_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 207.01206970214844 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MyVanna_ApiEndpoints_0" d="M433.53260524177165,543.9843711853027L422.29748292216226,530.3794938647202Q412.88721084594727,518.9843711853027 412.88721084594727,504.2059395558177L412.88721084594727,483.48523902893066L412.88721084594727,456.3446380979688Q412.88721084594727,447.9861068725586 420.9007447920051,445.6094878577528L524.0132497283346,415.0288299925059"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDE0LjE0NTg3MjExNjA4ODg3LCJ5Ijo3MzUuNjI0MTIzMjA1NTE2MX0seyJ4IjozMDkuNDk4MjkxMDE1NjI1LCJ5Ijo4MDIuOTY3MDIxOTQyMTM4N30seyJ4IjozMDkuNDk4MjkxMDE1NjI1LCJ5Ijo4NDguOTY1Mjg2MjU0ODgyOH0seyJ4IjozMDkuNDk4MjkxMDE1NjI1LCJ5Ijo4OTQuOTYzNTUwNTY3NjI3fSx7IngiOjI3MS45MDU5MzM1NDI3MzAzLCJ5Ijo5MTkuOTYzNTUwNTY3NjI3fV0=" data-id="L_MyVanna_OllamaLLM_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 245.2557373046875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MyVanna_OllamaLLM_0" d="M414.14587211608887,735.6241232055161L318.2765381163221,797.3180370409036Q309.498291015625,802.9670219421387 309.498291015625,813.4058270737272L309.498291015625,848.9652862548828L309.498291015625,884.3783610545831Q309.498291015625,894.963550567627 300.68422370282093,900.8251590868315L275.23665040970474,917.7485279600558"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTQxLjA5MjM2NTEwMjI4OTI1LCJ5Ijo5MTkuOTYzNTUwNTY3NjI3fSx7IngiOjEwMy41MDAwMDc2MjkzOTQ1MywieSI6ODk0Ljk2MzU1MDU2NzYyN30seyJ4IjoxMDMuNTAwMDA3NjI5Mzk0NTMsInkiOjg0OC45NjUyODYyNTQ4ODI4fSx7IngiOjEwMy41MDAwMDc2MjkzOTQ1MywieSI6ODAyLjk2NzAyMTk0MjEzODd9LHsieCI6NDE0LjE0NTg3MjExNjA4ODg3LCJ5Ijo2OTkuNTgxNDc0MDc3MjM2MX1d" data-id="L_OllamaLLM_MyVanna_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 447.78094482421875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OllamaLLM_MyVanna_0" d="M141.09236510228925,919.963550567627L112.31407494219857,900.8251590868315Q103.50000762939453,894.963550567627 103.50000762939453,884.3783610545833L103.50000762939453,848.9652862548828L103.50000762939453,811.5154634662247Q103.50000762939453,802.9670219421387 111.61104779008369,800.2675997813038L410.3505416396105,700.844591876301"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTE3LjY2NTMwMzE0MjUzNSwieSI6Nzc3Ljk2NzAyMTk0MjEzODd9LHsieCI6NTE0Ljk5ODMxMzkwMzgwODYsInkiOjgwMi45NjcwMjE5NDIxMzg3fSx7IngiOjUxNC45OTgzMTM5MDM4MDg2LCJ5Ijo4NDguOTY1Mjg2MjU0ODgyOH0seyJ4Ijo1MTQuOTk4MzEzOTAzODA4NiwieSI6ODk0Ljk2MzU1MDU2NzYyN30seyJ4Ijo1MTQuOTk4MzEzOTAzODA4NiwieSI6OTYzLjQ2MDk0NTEyOTM5NDV9LHsieCI6NTE0Ljk5ODMxMzkwMzgwODYsInkiOjEwMzEuOTU4MzM5NjkxMTYyfSx7IngiOjUxNC45OTgzMTM5MDM4MDg2LCJ5IjoxMDU2Ljk1ODMzOTY5MTE2Mn0seyJ4Ijo1MTQuOTk4MzEzOTAzODA4NiwieSI6MTA4MS45NTgzMzk2OTExNjJ9LHsieCI6NTE0Ljk5ODMxMzkwMzgwODYsInkiOjExMDYuOTU4MzM5NjkxMTYyfV0=" data-id="L_MyVanna_PostgresDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 316.10955810546875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MyVanna_PostgresDB_0" d="M517.665303142535,777.9670219421387L516.3318085231717,790.4670219421387Q514.9983139038086,802.9670219421387 514.9983139038086,815.5379490320502L514.9983139038086,848.9652862548828L514.9983139038086,894.963550567627L514.9983139038086,963.4609451293945L514.9983139038086,1031.958339691162L514.9983139038086,1056.958339691162L514.9983139038086,1081.958339691162L514.9983139038086,1102.958339691162"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTM3LjI5NzEzMzk1OTM1MDYsInkiOjIyOS40OTM5MjMxODcyNTU4Nn0seyJ4Ijo5NjkuMjkyNjQ0NTAwNzMyNCwieSI6MjY0Ljk5MzA1MzQzNjI3OTN9LHsieCI6OTY5LjI5MjY0NDUwMDczMjQsInkiOjMwMC40OTIxODU1OTI2NTEzN30seyJ4Ijo5NjkuMjkyNjQ0NTAwNzMyNCwieSI6Mzc5LjQ4ODcxMjMxMDc5MX0seyJ4Ijo5NjkuMjkyNjQ0NTAwNzMyNCwieSI6NDQ3Ljk4NjEwNjg3MjU1ODZ9LHsieCI6OTY5LjI5MjY0NDUwMDczMjQsInkiOjQ4My40ODUyMzkwMjg5MzA2Nn0seyJ4Ijo5NjkuMjkyNjQ0NTAwNzMyNCwieSI6NTE4Ljk4NDM3MTE4NTMwMjd9LHsieCI6OTY5LjI5MjY0NDUwMDczMjQsInkiOjY2MC45NzU2OTY1NjM3MjA3fSx7IngiOjk2OS4yOTI2NDQ1MDA3MzI0LCJ5Ijo4MDIuOTY3MDIxOTQyMTM4N30seyJ4Ijo5NjkuMjkyNjQ0NTAwNzMyNCwieSI6ODQ4Ljk2NTI4NjI1NDg4Mjh9LHsieCI6OTY5LjI5MjY0NDUwMDczMjQsInkiOjg5NC45NjM1NTA1Njc2Mjd9LHsieCI6OTY5LjI5MjY0NDUwMDczMjQsInkiOjk2My40NjA5NDUxMjkzOTQ1fSx7IngiOjk2OS4yOTI2NDQ1MDA3MzI0LCJ5IjoxMDMxLjk1ODMzOTY5MTE2Mn0seyJ4Ijo5NjkuMjkyNjQ0NTAwNzMyNCwieSI6MTA1Ni45NTgzMzk2OTExNjJ9LHsieCI6OTY5LjI5MjY0NDUwMDczMjQsInkiOjEwODEuOTU4MzM5NjkxMTYyfSx7IngiOjkzMS41MDcwMjM0OTM2ODIsInkiOjExMDYuOTU4MzM5NjkxMTYyfV0=" data-id="L_FlaskCore_AppCache_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 894.1788330078125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FlaskCore_AppCache_0" d="M937.2971339593506,229.49392318725586L959.9577307619106,254.63593416952347Q969.2926445007324,264.9930534362793 969.2926445007324,278.9361712374402L969.2926445007324,300.49218559265137L969.2926445007324,379.488712310791L969.2926445007324,447.9861068725586L969.2926445007324,483.48523902893066L969.2926445007324,518.9843711853027L969.2926445007324,660.9756965637207L969.2926445007324,802.9670219421387L969.2926445007324,848.9652862548828L969.2926445007324,894.963550567627L969.2926445007324,963.4609451293945L969.2926445007324,1031.958339691162L969.2926445007324,1056.958339691162L969.2926445007324,1071.396419324594Q969.2926445007324,1081.958339691162 960.4841621430476,1087.7862724622294L934.8429631909994,1104.7511907755104"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NzU5Ljg0ODE4MDc3MDg3NCwieSI6NDEyLjMwMTgxMzQ1OTIzMjZ9LHsieCI6ODg1Ljk5ODM1OTY4MDE3NTgsInkiOjQ0Ny45ODYxMDY4NzI1NTg2fSx7IngiOjg4NS45OTgzNTk2ODAxNzU4LCJ5Ijo0ODMuNDg1MjM5MDI4OTMwNjZ9LHsieCI6ODg1Ljk5ODM1OTY4MDE3NTgsInkiOjUxOC45ODQzNzExODUzMDI3fSx7IngiOjg4NS45OTgzNTk2ODAxNzU4LCJ5Ijo2NjAuOTc1Njk2NTYzNzIwN30seyJ4Ijo4ODUuOTk4MzU5NjgwMTc1OCwieSI6ODAyLjk2NzAyMTk0MjEzODd9LHsieCI6ODg1Ljk5ODM1OTY4MDE3NTgsInkiOjg0OC45NjUyODYyNTQ4ODI4fSx7IngiOjg4NS45OTgzNTk2ODAxNzU4LCJ5Ijo4OTQuOTYzNTUwNTY3NjI3fSx7IngiOjg4NS45OTgzNTk2ODAxNzU4LCJ5Ijo5NjMuNDYwOTQ1MTI5Mzk0NX0seyJ4Ijo4ODUuOTk4MzU5NjgwMTc1OCwieSI6MTAzMS45NTgzMzk2OTExNjJ9LHsieCI6ODg1Ljk5ODM1OTY4MDE3NTgsInkiOjEwNTYuOTU4MzM5NjkxMTYyfSx7IngiOjg4NS45OTgzNTk2ODAxNzU4LCJ5IjoxMDgxLjk1ODMzOTY5MTE2Mn0seyJ4Ijo4NzguNjEzMjY4OTk3NDc0NCwieSI6MTEwNi45NTgzMzk2OTExNjJ9XQ==" data-id="L_ApiEndpoints_AppCache_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 775.8031616210938 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApiEndpoints_AppCache_0" d="M759.848180770874,412.3018134592326L878.0227995201116,445.730048012016Q885.9983596801758,447.9861068725586 885.9983596801758,456.2746146664281L885.9983596801758,483.48523902893066L885.9983596801758,518.9843711853027L885.9983596801758,660.9756965637207L885.9983596801758,802.9670219421387L885.9983596801758,848.9652862548828L885.9983596801758,894.963550567627L885.9983596801758,963.4609451293945L885.9983596801758,1031.958339691162L885.9983596801758,1056.958339691162L885.9983596801758,1070.9243498531298Q885.9983596801758,1081.958339691162 882.8724167970495,1092.540277738478L879.7464739139233,1103.122215785794"/><path marker-end="url(#export-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjQ2LjE0NTkwMjYzMzY2NywieSI6NzMwLjgxMTcxMjMzNzE5M30seyJ4Ijo3NjUuOTk4MzQ0NDIxMzg2NywieSI6ODAyLjk2NzAyMTk0MjEzODd9LHsieCI6NzY1Ljk5ODM0NDQyMTM4NjcsInkiOjg0OC45NjUyODYyNTQ4ODI4fSx7IngiOjc2NS45OTgzNDQ0MjEzODY3LCJ5Ijo4OTQuOTYzNTUwNTY3NjI3fSx7IngiOjc2NS45OTgzNDQ0MjEzODY3LCJ5Ijo5NjMuNDYwOTQ1MTI5Mzk0NX0seyJ4Ijo3NjUuOTk4MzQ0NDIxMzg2NywieSI6MTAzMS45NTgzMzk2OTExNjJ9LHsieCI6NzY1Ljk5ODM0NDQyMTM4NjcsInkiOjEwNTYuOTU4MzM5NjkxMTYyfSx7IngiOjc2NS45OTgzNDQ0MjEzODY3LCJ5IjoxMDgxLjk1ODMzOTY5MTE2Mn0seyJ4Ijo4MDIuNDEwNTQ1NjAxNTgyLCJ5IjoxMTA2Ljk1ODMzOTY5MTE2Mn1d" data-id="L_MyVanna_AppCache_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 446.7000427246094 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MyVanna_AppCache_0" d="M646.145902633667,730.811712337193L757.2926657418087,797.7259026868571Q765.9983444213867,802.9670219421387 765.9983444213867,813.1286247842174L765.9983444213867,848.9652862548828L765.9983444213867,894.963550567627L765.9983444213867,963.4609451293945L765.9983444213867,1031.958339691162L765.9983444213867,1056.958339691162L765.9983444213867,1071.2246794714479Q765.9983444213867,1081.958339691162 774.8471166557111,1088.033756685684L799.1129669366371,1104.6942780867662"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_User_WebAppUI_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_AppConfig_FlaskCore_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_FlaskCore_ApiEndpoints_0" class="label"><foreignObject height="0" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(668.1745586395264, 300.49218559265137)" class="edgeLabel"><g transform="translate(-49.67014694213867, -10.49913215637207)" data-id="L_WebAppUI_ApiEndpoints_0" class="label"><foreignObject height="20.99826431274414" width="99.34029388427734"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>HTTP Requests</p></span></div></foreignObject></g></g><g transform="translate(453.1997184753418, 300.49218559265137)" class="edgeLabel"><g transform="translate(-60.31250762939453, -10.49913215637207)" data-id="L_ApiEndpoints_WebAppUI_0" class="label"><foreignObject height="20.99826431274414" width="120.62501525878906"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Serves Pages/Data</p></span></div></foreignObject></g></g><g transform="translate(646.729248046875, 483.48523902893066)" class="edgeLabel"><g transform="translate(-82.75173950195312, -10.49913215637207)" data-id="L_ApiEndpoints_MyVanna_0" class="label"><foreignObject height="20.99826431274414" width="165.50347900390625"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Delegates AI &amp; Data Tasks</p></span></div></foreignObject></g></g><g transform="translate(412.88721084594727, 483.48523902893066)" class="edgeLabel"><g transform="translate(-66.52777862548828, -10.49913215637207)" data-id="L_MyVanna_ApiEndpoints_0" class="label"><foreignObject height="20.99826431274414" width="133.05555725097656"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Returns Results/Data</p></span></div></foreignObject></g></g><g transform="translate(309.498291015625, 848.9652862548828)" class="edgeLabel"><g transform="translate(-100.00001525878906, -20.99826431274414)" data-id="L_MyVanna_OllamaLLM_0" class="label"><foreignObject height="41.99652862548828" width="200.00003051757812"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Constructs Prompts &amp; Sends Queries</p></span></div></foreignObject></g></g><g transform="translate(103.50000762939453, 848.9652862548828)" class="edgeLabel"><g transform="translate(-85.9982681274414, -10.49913215637207)" data-id="L_OllamaLLM_MyVanna_0" class="label"><foreignObject height="20.99826431274414" width="171.9965362548828"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Returns Generated Content</p></span></div></foreignObject></g></g><g transform="translate(514.9983139038086, 963.4609451293945)" class="edgeLabel"><g transform="translate(-100.00001525878906, -20.99826431274414)" data-id="L_MyVanna_PostgresDB_0" class="label"><foreignObject height="41.99652862548828" width="200.00003051757812"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Executes SQL, Fetches Schema for Context</p></span></div></foreignObject></g></g><g transform="translate(969.2926445007324, 660.9756965637207)" class="edgeLabel"><g transform="translate(-59.53125762939453, -10.49913215637207)" data-id="L_FlaskCore_AppCache_0" class="label"><foreignObject height="20.99826431274414" width="119.06251525878906"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Initializes/Manages</p></span></div></foreignObject></g></g><g transform="translate(885.9983596801758, 848.9652862548828)" class="edgeLabel"><g transform="translate(-41.888023376464844, -10.49913215637207)" data-id="L_ApiEndpoints_AppCache_0" class="label"><foreignObject height="20.99826431274414" width="83.77604675292969"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Reads/Writes</p></span></div></foreignObject></g></g><g transform="translate(765.9983444213867, 963.4609451293945)" class="edgeLabel"><g transform="translate(-100.00001525878906, -20.99826431274414)" data-id="L_MyVanna_AppCache_0" class="label"><foreignObject height="41.99652862548828" width="200.00003051757812"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Interacts with for Chat History/Context</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(198.498291015625, 196.49565887451172)" data-look="neo" data-et="node" data-node="true" data-id="WebAppUI" id="flowchart-WebAppUI-0" class="node default client"><rect stroke="url(#gradient)" height="86.99479293823242" width="232.00003051757812" y="-43.49739646911621" x="-116.00001525878906" data-id="WebAppUI" style="fill:#e6e6fa !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100.00001525878906, -31.49739646911621)" style="" class="label"><rect/><foreignObject height="62.99479293823242" width="200.00003051757812"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Web Browser<br />(Login, Dashboard, Chatbot Pages)</p></span></div></foreignObject></g></g><g transform="translate(907.5556640625, 196.49565887451172)" data-look="neo" data-et="node" data-node="true" data-id="FlaskCore" id="flowchart-FlaskCore-1" class="node default backend"><rect stroke="url(#gradient)" height="65.99652862548828" width="216.9479217529297" y="-32.99826431274414" x="-108.47396087646484" data-id="FlaskCore" style="fill:#add8e6 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-92.47396087646484, -20.99826431274414)" style="" class="label"><rect/><foreignObject height="41.99652862548828" width="184.9479217529297"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Flask Core Engine<br />(app/<strong>init</strong>.py, Manages Cache)</p></span></div></foreignObject></g></g><g transform="translate(643.848165512085, 379.488712310791)" data-look="neo" data-et="node" data-node="true" data-id="ApiEndpoints" id="flowchart-ApiEndpoints-2" class="node default backend"><rect stroke="url(#gradient)" height="86.99479293823242" width="232.00003051757812" y="-43.49739646911621" x="-116.00001525878906" data-id="ApiEndpoints" style="fill:#add8e6 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100.00001525878906, -31.49739646911621)" style="" class="label"><rect/><foreignObject height="62.99479293823242" width="200.00003051757812"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>API Endpoints<br />(Auth, SQL Gen/Exec, Analysis, Training Mgmt)</p></span></div></foreignObject></g></g><g transform="translate(907.5556640625, 55.49913215637207)" data-look="neo" data-et="node" data-node="true" data-id="AppConfig" id="flowchart-AppConfig-3" class="node default backend"><rect stroke="url(#gradient)" height="44.99826431274414" width="184.5260467529297" y="-22.49913215637207" x="-92.26302337646484" data-id="AppConfig" style="fill:#add8e6 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-76.26302337646484, -10.49913215637207)" style="" class="label"><rect/><foreignObject height="20.99826431274414" width="152.5260467529297"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Configuration (config.py)</p></span></div></foreignObject></g></g><g transform="translate(530.1458873748779, 660.9756965637207)" data-look="neo" data-et="node" data-node="true" data-id="MyVanna" id="flowchart-MyVanna-4" class="node default vanna_engine"><rect stroke="url(#gradient)" height="233.98265075683594" width="232.00003051757812" y="-116.99132537841797" x="-116.00001525878906" data-id="MyVanna" style="fill:#90ee90 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100.00001525878906, -104.99132537841797)" style="" class="label"><rect/><foreignObject height="209.98265075683594" width="200.00003051757812"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>MyVanna (Custom Implementation)<br />Handles: NL-to-SQL &amp; Correction,<br />SQL Execution, LLM Prompt Engineering,<br />Data Summarization &amp; Charting Logic,<br />Context Retrieval (DDL, Docs, Q/A Pairs)</p></span></div></foreignObject></g></g><g transform="translate(206.49914932250977, 963.4609451293945)" data-look="neo" data-et="node" data-node="true" data-id="OllamaLLM" id="flowchart-OllamaLLM-5" class="node default external_ai"><rect stroke="url(#gradient)" height="86.99479293823242" width="232.00003051757812" y="-43.49739646911621" x="-116.00001525878906" data-id="OllamaLLM" style="fill:#ffcc99 !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100.00001525878906, -31.49739646911621)" style="" class="label"><rect/><foreignObject height="62.99479293823242" width="200.00003051757812"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Ollama LLM<br />(Generates SQL, Summaries, Chart Code, Corrections)</p></span></div></foreignObject></g></g><g transform="translate(514.9983139038086, 1150.4557342529297)" data-look="neo" data-et="node" data-node="true" data-id="PostgresDB" id="flowchart-PostgresDB-6" class="node default storage"><rect stroke="url(#gradient)" height="86.99479293823242" width="232.00003051757812" y="-43.49739646911621" x="-116.00001525878906" data-id="PostgresDB" style="fill:#lightyellow !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100.00001525878906, -31.49739646911621)" style="" class="label"><rect/><foreignObject height="62.99479293823242" width="200.00003051757812"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>PostgreSQL Database<br />(Business Data, Schema for Context)</p></span></div></foreignObject></g></g><g transform="translate(865.7639808654785, 1150.4557342529297)" data-look="neo" data-et="node" data-node="true" data-id="AppCache" id="flowchart-AppCache-7" class="node default storage"><rect stroke="url(#gradient)" height="86.99479293823242" width="232.00003051757812" y="-43.49739646911621" x="-116.00001525878906" data-id="AppCache" style="fill:#lightyellow !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100.00001525878906, -31.49739646911621)" style="" class="label"><rect/><foreignObject height="62.99479293823242" width="200.00003051757812"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Application Cache<br />(Session Data, Query Results, Chat History)</p></span></div></foreignObject></g></g><g transform="translate(198.498291015625, 55.49913215637207)" data-look="neo" data-et="node" data-node="true" data-id="User" id="flowchart-User-8" class="node default client"><rect stroke="url(#gradient)" height="44.99826431274414" width="61.56597328186035" y="-22.49913215637207" x="-30.782986640930176" data-id="User" style="fill:#e6e6fa !important;stroke:#333 !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-14.782986640930176, -10.49913215637207)" style="" class="label"><rect/><foreignObject height="20.99826431274414" width="29.56597328186035"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>User</p></span></div></foreignObject></g></g></g></g></g><defs><filter width="130%" height="130%" id="drop-shadow"><feDropShadow flood-color="#000000" flood-opacity="0.06" stdDeviation="0" dy="4" dx="4"/></filter></defs><defs><filter width="150%" height="150%" id="drop-shadow-small"><feDropShadow flood-color="#000000" flood-opacity="0.06" stdDeviation="0" dy="2" dx="2"/></filter></defs><linearGradient y2="0%" x2="100%" y1="0%" x1="0%" gradientUnits="objectBoundingBox" id="export-svg-gradient"><stop stop-opacity="1" stop-color="#0042eb" offset="0%"/><stop stop-opacity="1" stop-color="#eb0042" offset="100%"/></linearGradient></svg>