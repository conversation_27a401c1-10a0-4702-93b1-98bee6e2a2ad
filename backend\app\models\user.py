from typing import Optional
from datetime import datetime
from passlib.context import CryptContext
from app.core.app_database import get_app_database_service

# Password hashing context (consistent with sync script)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class User:
    """User model for FastAPI authentication"""

    def __init__(
        self,
        id: int,
        username: str,
        password_hash: str,
        role: str,
        first_name: str = "",
        last_name: str = "",
        department_name: str = "",
        last_login: Optional[datetime] = None,
        is_active: bool = True,
    ):
        self.id = id
        self.username = username
        self.password_hash = password_hash  # Store bcrypt hashed passwords
        self.role = role
        self.first_name = first_name
        self.last_name = last_name
        self.department_name = department_name
        self.last_login = last_login
        self.is_active = is_active
        # Initialize password history
        self.password_history = [password_hash] if password_hash else []

    def get_id(self):
        return str(self.id)

    @property
    def full_name(self):
        """Get the user's full name"""
        return f"{self.first_name} {self.last_name}".strip() or self.username

    @staticmethod
    def validate_password_complexity(password: str) -> bool:
        """Validate password meets complexity requirements"""
        if len(password) < 8:
            return False
        if not any(char.isdigit() for char in password):
            return False
        if not any(char.isupper() for char in password):
            return False
        if not any(char.islower() for char in password):
            return False
        if not any(char in "!@#$%^&*()_+" for char in password):
            return False
        return True

    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password using bcrypt"""
        return pwd_context.hash(password)

    def set_password(self, new_password: str):
        """Set a new password with complexity check and automatic hashing"""
        if not self.validate_password_complexity(new_password):
            raise ValueError("Password does not meet complexity requirements")

        # Store current password in history before changing
        self.password_history.append(self.password_hash)
        # Keep only last 5 passwords
        self.password_history = self.password_history[-5:]

        self.password_hash = self.hash_password(new_password)

    def check_password(self, password: str) -> bool:
        """Check if provided password matches the stored bcrypt hash"""
        return pwd_context.verify(password, self.password_hash)

    def is_password_in_history(self, password: str) -> bool:
        """Check if password matches any in history"""
        return any(pwd_context.verify(password, ph) for ph in self.password_history)


def get_user_by_id(user_id: str) -> Optional[User]:
    """Get user by ID from the app database"""
    try:
        db_service = get_app_database_service()
        user_data = db_service.get_user_by_id(int(user_id))

        if user_data:
            return User(
                id=user_data["id"],
                username=user_data["username"],
                password_hash=user_data["password_hash"],
                role=user_data["role_name"],
                first_name=user_data["first_name"] or "",
                last_name=user_data["last_name"] or "",
                department_name=user_data["department_name"] or "",
                last_login=None,
                is_active=True,
            )
        return None
    except Exception as e:
        print(f"Error getting user by ID {user_id}: {e}")
        return None


def get_user_by_username(username: str) -> Optional[User]:
    """Get user by username from the app database"""
    try:
        db_service = get_app_database_service()
        user_data = db_service.get_user_by_username(username)

        if user_data:
            return User(
                id=user_data["id"],
                username=user_data["username"],
                password_hash=user_data["password_hash"],
                role=user_data["role_name"],
                first_name=user_data["first_name"] or "",
                last_name=user_data["last_name"] or "",
                department_name=user_data["department_name"] or "",
                last_login=None,
                is_active=True,
            )
        return None
    except Exception as e:
        print(f"Error getting user by username {username}: {e}")
        return None


def update_user_last_login(username: str, last_login: datetime):
    """Update the user's last_login in the app database"""
    try:
        db_service = get_app_database_service()
        db_service.update_user_last_login(username, last_login)
    except Exception as e:
        print(f"Error updating last_login for user {username}: {e}")
