# 🚀 AI Hospital Chatbot: Dockerized Setup Guide

This guide will get you running the full stack (backend, frontend, and databases) in Docker containers, on any machine, with minimal effort.

---

## 1. Prerequisites

- [Docker](https://docs.docker.com/get-docker/) and [Docker Compose](https://docs.docker.com/compose/) installed

---

## 2. <PERSON>lone the Repository

```bash
git clone <your-repo-url>
cd AI-Chatbot-for-Hospital
```

---

## 3. Configure Environment Variables

1. Copy the example env file:

   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and fill in your secrets and config:
   - Set `USE_LOCAL_DB=true` to use Dockerized Postgres (recommended for local dev)
   - Set DB passwords for both hospital and app DBs
   - Set `OLLAMA_HOST=ollama` and `OLLAMA_PORT=11434` (default, for Docker Compose)
   - Change `SECRET_KEY` to something secure

---

## 4. Build and Start All Services

```bash
docker compose up --build
```

- This will build and start:
    - `hospital-db` (Postgres for hospital data)
    - `app-db` (Postgres for app/auth data)
    - `backend` (FastAPI app)
    - `frontend` (React app served by Nginx)

---

## 5. Access the Application

- **Frontend:** [http://localhost:3000](http://localhost:3000)
- **Backend API:** [http://localhost:8000/docs](http://localhost:8000/docs) (Swagger UI)

---

## 6. Stopping and Restarting

- Stop all containers:

  ```bash
  docker compose down
  ```

- Restart:

  ```bash
  docker compose up
  ```

---

## 7. Using a Remote Database

- Set `USE_LOCAL_DB=false` in `.env`
- Set `HOSPITAL_DB_HOST`, `APP_DB_HOST`, and all DB connection vars to your remote DB values
- Rebuild and restart:

  ```bash
  docker compose up --build
  ```

---

## 8. Troubleshooting

- Check logs for any service:

  ```bash
  docker compose logs backend
  docker compose logs frontend
  docker compose logs hospital-db
  docker compose logs app-db
  ```

- Make sure your `.env` is correct and all required variables are set
- If using a remote Ollama, ensure it is reachable from the backend container

---

## 9. Database Management

- You can connect to the Postgres DBs using any client at:
    - `localhost:5433` (hospital-db)
    - `localhost:5434` (app-db)
    - Use the credentials from your `.env`

---

## 10. Customization

- To use your own data, update the DBs or mount volumes as needed
- For advanced embedding or Qdrant, see the Embeding/ folder (not required for basic setup)

---

## 11. Updating

- Pull latest code:

  ```bash
  git pull
  docker compose build
  docker compose up
  ```

---

## 12. FAQ

- **Q: Can I use my own Postgres server?**
    - Yes! Set `USE_LOCAL_DB=false` and update DB vars in `.env`.
- **Q: Do I need Qdrant or Redis?**
    - No, not for the main app. Only for advanced embedding workflows.
- **Q: How do I reset the DBs?**
    - Stop containers, delete the `hospital-db-data` and `app-db-data` Docker volumes, and restart.

---

Happy hacking! 🚑🤖
